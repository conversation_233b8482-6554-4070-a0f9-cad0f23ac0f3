'use client';

import React from 'react';
import Image from 'next/image';

export const Footer = () => {
  return (
    <footer style={{ background: 'white', color: 'black' }} className="py-12 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4"><Image src="/logos/logo1.png" alt="PaydAI" width={128} height={32} /></h3>
            <p className="mb-4">
              Helping job seekers land more interviews with tailored
              applications.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="hover:underline transition-colors" style={{ color: 'black' }}>
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true" style={{ color: 'black' }}>
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="https://www.linkedin.com/company/payd-ai/posts/" target="_blank" rel="noopener noreferrer" className="hover:underline transition-colors" style={{ color: 'black' }}>
                <span className="sr-only">LinkedIn</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true" style={{ color: 'black' }}>
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Product</h4>
            <ul className="space-y-2">
              <li>
                <a href="#how-it-works" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Features
                </a>
              </li>
              <li>
                <a href="#pricing" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Pricing
                </a>
              </li>
              {/* <li>
                <a href="#" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Demo
                </a>
              </li> */}
              <li>
                <a href="#testimonials" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Testimonials
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Resources</h4>
            <ul className="space-y-2">
              <li>
                <a href="https://www.linkedin.com/company/payd-ai/posts/?feedView=articles&viewAsMember=true" target="_blank" rel="noopener noreferrer" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Blog
                </a>
              </li>
              <li>
                <a href="#" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Career Tips
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Company</h4>
            <ul className="space-y-2">
              <li>
                <a href="/about-us" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  About Us
                </a>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Contact
                </a>
              </li>
              <li>
                <a href="/privacy-policy" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="/terms-of-service" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Terms of Service
                </a>
              </li>
              <li>
                <a href="https://docs.google.com/document/d/196exqV2IDdFMIV32WO3G9bbEMLLA2NreVBZn2V1S5F0/edit?usp=sharing" target="_blank" rel="noopener noreferrer" className="hover:underline transition-colors" style={{ color: 'black' }}>
                  Cancellation policy
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t mt-12 pt-8 text-center text-sm" style={{ borderColor: 'black' }}>
          <p>&copy; {new Date().getFullYear()} PaydAI a YOLOVEDA - . All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
