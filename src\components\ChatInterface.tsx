'use client';

import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { ObjectId } from 'mongodb';
import { IChatMessage, ChatSessionResponse, ChatMessageResponse, SessionType } from '@/types/chat';
import { MatchResult } from '@/types/match';
import LoadingSpinner from './LoadingSpinner';
import { authenticatedFetch } from '@/lib/auth/token-storage';

interface ChatInterfaceProps {
  userId: string;
  matchResult: MatchResult;
  resumeHtml: string;
  jobDescription: string;
  onClose: () => void;
  onComplete: (finalResult: { htmlContent: string; changes: string[] }) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  userId,
  matchResult,
  resumeHtml,
  jobDescription,
  onClose,
  onComplete
}) => {
  const [messages, setMessages] = useState<IChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isSessionLoading, setIsSessionLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isComplete, setIsComplete] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize chat session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        setIsSessionLoading(true);
        setError('');

        const response = await authenticatedFetch(`/api/users/${userId}/chat/sessions`, {
          method: 'POST',
          body: JSON.stringify({
            sessionType: SessionType.RESUME_FINETUNE,
            context: {
              matchResult: matchResult
            }
          }),
        });

        const result: ChatSessionResponse = await response.json();

        if (!result.success) {
          throw new Error(result.error?.message || 'Failed to create chat session');
        }

        if (result.data) {
          setSessionId(result.data.sessionId);

          // Add initial message if provided
          if (result.data.initialMessage) {
            const initialMessage: IChatMessage = {
              sessionId: result.data.sessionId as unknown as ObjectId,
              role: 'assistant',
              content: result.data.initialMessage,
              messageType: 'initial',
              createdAt: new Date()
            };
            setMessages([initialMessage]);
          }

          // Log credit information for debugging
          if (result.data.creditsRemaining !== undefined) {
            console.log('Credits remaining after session creation:', result.data.creditsRemaining);
          }
        }
      } catch (err) {
        setError(`Failed to initialize chat: ${err instanceof Error ? err.message : 'Unknown error'}`);
        console.error('Chat initialization error:', err);
      } finally {
        setIsSessionLoading(false);
      }
    };

    initializeSession();
  }, [userId, matchResult, resumeHtml, jobDescription]);

  // Send message
  const sendMessage = async () => {
    if (!currentMessage.trim() || !sessionId || isLoading) return;

    const userMessage: IChatMessage = {
      sessionId: sessionId as unknown as ObjectId,
      role: 'user',
      content: currentMessage.trim(),
      messageType: 'user_response',
      createdAt: new Date()
    };

    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage('');
    setIsLoading(true);
    setError('');

    try {
      const response = await authenticatedFetch(`/api/users/${userId}/chat/${sessionId}/messages`, {
        method: 'POST',
        body: JSON.stringify({
          message: currentMessage.trim(),
          messageType: 'user_response'
        }),
      });

      const result: ChatMessageResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to send message');
      }

      if (result.data) {
        // Add assistant response
        const assistantMessage: IChatMessage = {
          sessionId: sessionId as unknown as ObjectId,
          role: 'assistant',
          content: result.data.response,
          messageType: 'assistant_response',
          createdAt: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);

        // Check if conversation is complete
        if (result.data.isComplete) {
          setIsComplete(true);

          onComplete({
            htmlContent: (result.data?.metadata?.finalResume as string) || '',
            changes: (result.data?.metadata?.changes as string[]) || []
          });
        }
      }
    } catch (err) {
      setError(`Failed to send message: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error('Send message error:', err);
    } finally {
      setIsLoading(false);
    }
  };


  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (isSessionLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8">
        <div className="flex items-center justify-center">
          <LoadingSpinner size="large" />
          <span className="ml-3 text-lg">Initializing chat session...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md flex flex-col h-[600px]">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Resume Fine-tuning Chat</h2>
          <p className="text-sm text-gray-600 mt-1">
            Let us optimize your resume for this job opportunity through conversation
          </p>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[70%] rounded-lg px-4 py-3 ${message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-900'
                }`}
            >
              <div className={message.role === 'user' ? 'whitespace-pre-wrap' : ''}>
                {message.role === 'user' ? (
                  message.content
                ) : (
                  <div className="prose prose-sm max-w-none prose-headings:text-gray-900 prose-p:text-gray-900 prose-strong:text-gray-900 prose-em:text-gray-700 prose-code:text-gray-800 prose-code:bg-gray-200 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-200 prose-pre:text-gray-800">
                    <ReactMarkdown
                      components={{
                        // Customize code blocks
                        code: ({ children, className, ...props }) => {
                          const isInline = !className?.includes('language-');
                          return isInline ? (
                            <code
                              className="bg-gray-200 text-gray-800 px-1 py-0.5 rounded text-sm"
                              {...props}
                            >
                              {children}
                            </code>
                          ) : (
                            <code
                              className="block bg-gray-200 text-gray-800 p-2 rounded text-sm overflow-x-auto"
                              {...props}
                            >
                              {children}
                            </code>
                          );
                        },
                        // Customize paragraphs to have proper spacing
                        p: ({ children }) => (
                          <p className="mb-2 last:mb-0">{children}</p>
                        ),
                        // Customize headings
                        h1: ({ children }) => (
                          <h1 className="text-lg font-semibold mb-2 text-gray-900">{children}</h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-base font-semibold mb-2 text-gray-900">{children}</h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-sm font-semibold mb-1 text-gray-900">{children}</h3>
                        ),
                        // Customize lists
                        ul: ({ children }) => (
                          <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
                        ),
                        li: ({ children }) => (
                          <li className="text-gray-900">{children}</li>
                        ),
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
              <div className={`text-xs mt-2 ${message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                {new Date(message.createdAt).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-4 py-3">
              <div className="flex items-center space-x-2">
                <LoadingSpinner size="small" />
                <span className="text-gray-600">PaydAI is on super-intelligence mode to re-engineer your resume</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      {!isComplete && (
        <div className="border-t p-6">
          <div className="flex space-x-4">
            <textarea
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your response here..."
              className="flex-1 border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              rows={3}
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!currentMessage.trim() || isLoading}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${!currentMessage.trim() || isLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
            >
              Send
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Press Enter to send, Shift+Enter for new line
          </p>
        </div>
      )}

      {isComplete && (
        <div className="border-t p-6 bg-green-50">
          <div className="flex items-center justify-center space-x-2 text-green-700">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">Resume optimization complete!</span>
          </div>
          <p className="text-sm text-green-600 text-center mt-2">
            Your optimized resume is ready. You can close this chat to view the results.
          </p>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
