import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import connectToDatabase from '../lib/mongodb';
import User from '../models/User';
import UserProfile from '../models/UserProfile';
import { parseResumePdf } from '../lib/pdf/pdf-parser';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Seeds a test user with a resume
 */
async function seedUserWithResume() {
  try {
    console.log('Connecting to database...');
    await connectToDatabase();

    // Check if we already have a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      console.log('Creating test user...');
      testUser = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });
      console.log(`Created test user with ID: ${testUser._id}`);
    } else {
      console.log(`Found existing test user with ID: ${testUser._id}`);
    }

    // Check if the user already has a profile with resume
    const existingProfile = await UserProfile.findOne({ user: testUser._id });
    
    if (existingProfile) {
      console.log('Test user already has a resume profile');
      console.log('Seed completed successfully!');
      process.exit(0);
      return;
    }

    // Path to a sample PDF resume (you'll need to provide this)
    const sampleResumePath = path.join(__dirname, '../../sample-resume.pdf');
    
    // Check if the sample resume exists
    if (!fs.existsSync(sampleResumePath)) {
      console.error('Sample resume file not found at:', sampleResumePath);
      console.error('Please place a sample-resume.pdf file in the project root directory');
      process.exit(1);
      return;
    }

    // Read the sample resume file
    const fileBuffer = fs.readFileSync(sampleResumePath);
    
    // Parse the PDF content
    console.log('Parsing sample resume...');
    const parsedResume = await parseResumePdf(fileBuffer);

    // Create the user profile with resume data
    console.log('Creating user profile with resume data...');
    const userProfile = await UserProfile.create({
      user: testUser._id,
      resumeData: {
        rawText: parsedResume.rawText,
        fileName: 'sample-resume.pdf',
        fileSize: fileBuffer.length,
        mimeType: 'application/pdf',
        uploadDate: new Date(),
        pdfBuffer: fileBuffer,
      },
    });

    console.log(`Created user profile with ID: ${userProfile._id}`);
    console.log('Seed completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding user with resume:', error);
    process.exit(1);
  }
}

// Run the seed function
seedUserWithResume();
