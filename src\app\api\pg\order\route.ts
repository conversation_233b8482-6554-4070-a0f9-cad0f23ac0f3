import { NextResponse } from "next/server";
import { Cashfree, CFEnvironment } from "cashfree-pg";
import Transaction from "@/models/Transaction";
import connectToDatabase from "@/lib/mongodb";
import { withNextAuth } from "@/lib/auth/middleware";

export const runtime = "nodejs"; // Ensure Node.js runtime (cashfree-pg is server-side SDK)
export const dynamic = "force-dynamic"; // Avoid caching for API route

// Read configuration from environment variables
const { CASHFREE_CLIENT_ID, CASHFREE_CLIENT_SECRET, CASHFREE_ENV } =
  process.env as Record<string, string | undefined>;

const cfEnv =
  (CASHFREE_ENV || "sandbox").toLowerCase() === "production"
    ? CFEnvironment.PRODUCTION
    : CFEnvironment.SANDBOX;

export const POST = withNextAuth(async (req, context, authenticatedUser) => {
  try {
    // Connect to database
    await connectToDatabase();
    
    if (!CASHFREE_CLIENT_ID || !CASHFREE_CLIENT_SECRET) {
      return NextResponse.json(
        {
          ok: false,
          error: {
            message: "Cashfree credentials are not configured.",
          },
        },
        { status: 500 }
      );
    }

    // Instantiate SDK per request to avoid stale config in hot-reload
    const cashfree = new Cashfree(
      cfEnv,
      CASHFREE_CLIENT_ID,
      CASHFREE_CLIENT_SECRET
    );

    const body = (await req.json().catch(() => ({}))) as any;

    // Build order request with provided payload or sensible defaults (sample values)
    const orderRequest: any = {
      order_amount: body?.order_amount,
      order_currency: "INR",
      customer_details: {
        customer_id: authenticatedUser.userId,
        customer_email: authenticatedUser.email,
        customer_phone: body?.customer_details?.customer_phone || "9999999999",
      },
    };

    // Optionally include order_id if client sends one
    if (body?.order_id) {
      orderRequest.order_id = body.order_id;
    }

    const cfRes = await cashfree.PGCreateOrder(orderRequest);
    const data = cfRes?.data ?? null;

    console.log("Cashfree create order response:", cfRes);

    // Create transaction record
    if (data) {
      try {
        await Transaction.create({
          userId: authenticatedUser.userId,
          orderId: data.order_id,
          paymentSessionId: data.payment_session_id,
          amount: parseFloat(body?.order_amount || "0") * 100, // Convert to paise
          currency: orderRequest.order_currency,
          status: "PENDING",
          paymentGateway: "cashfree",
          customerDetails: {
            customerId: authenticatedUser.userId,
            customerEmail: authenticatedUser.email,
            customerPhone: body?.customer_details?.customer_phone,
          },
          orderDetails: {
            orderAmount: parseFloat(body?.order_amount || "0") * 100, // Convert to paise
            orderCurrency: orderRequest.order_currency,
          },
        });
      } catch (transactionError) {
        console.error("Failed to create transaction record:", transactionError);
        // Continue with the response even if transaction creation fails
        // This ensures payment flow isn't broken due to DB issues
      }
    }

    return NextResponse.json(
      {
        ok: true,
        order: data,
      },
      { status: 200 }
    );
  } catch (err: any) {
    // Capture Cashfree error response when available
    const status = err?.response?.status ?? 500;
    const errorPayload = err?.response?.data ?? {
      message: err?.message || "Unknown error while creating order",
    };

    return NextResponse.json(
      {
        ok: false,
        error: errorPayload,
      },
      { status }
    );
  }
});
