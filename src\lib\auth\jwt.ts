import { SignJWT, jwtVerify, type JWTPayload as JoseJ<PERSON>TPayload } from 'jose';
import { NextRequest, NextResponse } from 'next/server';

// JWT secret - in production, this should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// Convert secret to Uint8Array for jose
const secret = new TextEncoder().encode(JWT_SECRET);

export interface JWTPayload {
  userId: string;
  email: string;
  roles: ('admin' | 'user')[];
  iat?: number;
  exp?: number;
}

/**
 * Convert expiration time string to seconds
 * @param expiresIn Time string like '7d', '24h', '60m'
 * @returns Number of seconds
 */
function parseExpirationTime(expiresIn: string): string {
  const match = expiresIn.match(/^(\d+)([dhm])$/);
  if (!match) {
    throw new Error('Invalid expiration time format');
  }
  
  const value = parseInt(match[1]);
  const unit = match[2];
  
  switch (unit) {
    case 'd':
      return `${value * 24 * 60 * 60}s`;
    case 'h':
      return `${value * 60 * 60}s`;
    case 'm':
      return `${value * 60}s`;
    default:
      throw new Error('Invalid time unit');
  }
}

/**
 * Generate a JWT token for a user
 * @param payload The payload to include in the token
 * @returns The generated JWT token
 */
export async function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string> {
  const expirationTime = parseExpirationTime(JWT_EXPIRES_IN);
  
  const jwt = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expirationTime)
    .sign(secret);
    
  return jwt;
}

/**
 * Verify and decode a JWT token
 * @param token The JWT token to verify
 * @returns The decoded payload or null if invalid
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secret);
    // Extract our custom fields from the jose payload
    const josePayload = payload as JoseJWTPayload & JWTPayload;
    return {
      userId: josePayload.userId,
      email: josePayload.email,
      roles: josePayload.roles || ['user'], // Default to 'user' role if not present
      iat: josePayload.iat,
      exp: josePayload.exp,
    };
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

/**
 * Extract JWT token from Authorization header
 * @param request The Next.js request object
 * @returns The token string or null if not found
 */
export function extractTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader) {
    return null;
  }
  
  // Check for Bearer token format
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
}

/**
 * Middleware function to validate JWT token from request
 * @param request The Next.js request object
 * @returns The decoded payload or null if invalid/missing
 */
export async function validateTokenFromRequest(request: NextRequest): Promise<JWTPayload | null> {
  const token = extractTokenFromRequest(request);
  
  if (!token) {
    return null;
  }
  
  return await verifyToken(token);
}

/**
 * Create an unauthorized response
 * @param message Optional error message
 * @returns NextResponse with 401 status
 */
export function createUnauthorizedResponse(message: string = 'Unauthorized') {
  return NextResponse.json(
    {
      success: false,
      error: {
        message,
      },
    },
    { status: 401 }
  );
}
