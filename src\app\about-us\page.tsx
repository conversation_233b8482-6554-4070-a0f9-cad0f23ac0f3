'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeftIcon, UsersIcon, TargetIcon, AwardIcon, HeartIcon } from 'lucide-react';

export default function AboutUs() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="text-white pt-8 ">
        <div className="container mx-auto px-4 max-w-6xl">
          <Link
            href="/"
            className="inline-flex items-center bg-black text-gray-300 hover:text-white px-4 py-2 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Home
          </Link>
        </div>
      </div>

      {/* Mission Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">About Us</h2>
            <div className="text-left max-w-4xl mx-auto space-y-6">
              <p className="text-xl text-gray-600">
                PaydAI was founded to solve one of the most persistent problems in today&apos;s professional world: helping individuals not just find jobs, but architect meaningful, sustainable career journeys.
              </p>
              <p className="text-xl text-gray-600">
                We&apos;ve witnessed firsthand how the traditional job search and career development process often leaves talented people misaligned with the roles that best fit their ambitions, skills, and passions. This enduring problem—of finding not just any job, but the right one, and building a sustainable, fulfilling journey of growth—motivates our every innovation.
              </p>
              <p className="text-xl text-gray-600 font-semibold">
                Our founding team is a unique blend of India&apos;s top technologists, product leaders, and digital innovators:
              </p>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-lg text-gray-700 mb-4">
                  <strong>Ram (Co-founder & CEO):</strong> An accidental IIT-IIM grad - tech strategist by day, artist-yogi by night. Over 27 years, Ram has directed technology at IBM, ThoughtWorks, Oracle and helped in edtech and skilling for Gov of India and The Nudge Foundation - and scaled Web1 to Web3 for startups.
                </p>
                <p className="text-lg text-gray-700">
                  <strong>Sharad (Co-founder, CTO/Mentor):</strong> A quintessential IIT-Madras alumnus and Ivy MBA - a product guru and CIO whisperer. In his 27+ years, Sharad has built and exited a startup, led enterprise products at IBM, Tally, and ANSR, and built groundbreaking HRMS/payroll solutions.
                </p>
              </div>
            </div>
          </div>

          {/* Mission Section */}
          <div className="bg-blue-50 rounded-2xl p-8 md:p-12 mb-20">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">Our Mission</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              <p className="text-xl text-gray-700">
                We&apos;re driven by one belief: careers aren&apos;t built on chance—they&apos;re built with intelligence, guidance, and growth. Today&apos;s platforms are fragmented and designed for volume, not for precision or human ambition. Millions spend hours fighting bots, blind filters, and broken tools.
              </p>
              <p className="text-xl text-gray-700">
                PaydAI is changing that—using cutting-edge AI to power the first agentic, AI-native career concierge. We help professionals:
              </p>
              <div className="grid md:grid-cols-1 gap-4 ml-6">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 mr-4 flex-shrink-0"></div>
                  <p className="text-lg text-gray-700"><strong>Prioritise intelligently:</strong> Target the right roles and bridge gaps, dramatically increasing selection odds.</p>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 mr-4 flex-shrink-0"></div>
                  <p className="text-lg text-gray-700"><strong>Present intelligently:</strong> Generate job-specific resumes and cover letters that boost interview conversion up to 5x.</p>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 mr-4 flex-shrink-0"></div>
                  <p className="text-lg text-gray-700"><strong>Perform and progress intelligently:</strong> Enable upskilling, coaching, and continuous growth through predictive, personalized nudges—all in a seamless, conversational experience.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Vision Section */}
          <div className="bg-gray-50 rounded-2xl p-8 md:p-12 mb-20">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">Our Vision</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              <p className="text-xl text-gray-700">
                In the next three years, PaydAI aims to become the go-to AI career platform, a super-intelligent career concierge for over 100 million users globally, unlocking lifelong career intelligence and opportunity for all.
              </p>
              <p className="text-xl text-gray-700">
                Whether you&apos;re a fresh graduate, a mid-career professional, or an organization seeking world-class talent, PaydAI&apos;s mission is your growth—powered by world-class technology, deep human insight, and an unwavering promise to empower every career journey.
              </p>
              <div className="text-center mt-8">
                <p className="text-2xl font-bold text-blue-600">
                  Enabling Careers. Powering Economies.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
