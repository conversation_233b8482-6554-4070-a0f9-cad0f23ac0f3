import { ObjectId } from 'mongodb';
import { MatchResult } from './match';

// Session type enum for better type safety
export enum SessionType {
  RESUME_FINETUNE = 'resume_finetune',
  GENERAL_CHAT = 'general_chat'
}

// Context interfaces for different session types
export interface ResumeFineTuneContext {
  resumeText: string;
  jobDescription: string;
  originalResume: string;
  matchResult: MatchResult;
}

export interface GeneralChatContext {
  topic?: string;
  [key: string]: unknown;
}

// Metadata interfaces for different session types
export interface ResumeFineTuneMetadata {
  currentQuestionIndex: number;
  matchScore: number;
  identifiedGaps: string[];
  strengths: string[];
  recommendations: string[];
  conversationComplete: boolean;
  finalResult?: {
    htmlContent: string;
    changes: string[];
  };
}

export interface GeneralChatMetadata {
  messageCount?: number;
  [key: string]: unknown;
}

// Union types for context and metadata based on session type
export type ChatSessionContext = ResumeFineTuneContext | GeneralChatContext;
export type ChatSessionMetadata = ResumeFineTuneMetadata | GeneralChatMetadata;

export interface IChatSession {
  _id?: ObjectId;
  userId: ObjectId;
  sessionType: SessionType | string; // Allow string for backward compatibility
  status: 'active' | 'completed' | 'abandoned';
  context: ChatSessionContext;
  metadata: ChatSessionMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface IChatMessage {
  _id?: ObjectId;
  sessionId: ObjectId;
  role: 'system' | 'assistant' | 'user';
  content: string;
  messageType: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
}

export interface ChatSessionResponse {
  success: boolean;
  data?: {
    sessionId: string;
    initialMessage?: string;
    context?: Record<string, unknown>;
    creditsRemaining?: number;
    totalUsed?: number;
  };
  error?: {
    message: string;
  };
}

export interface ChatMessageResponse {
  success: boolean;
  data?: {
    response: string;
    isComplete: boolean;
    metadata?: Record<string, unknown>;
  };
  error?: {
    message: string;
  };
}

export interface ChatHistoryResponse {
  success: boolean;
  data?: {
    session: IChatSession;
    messages: IChatMessage[];
  };
  error?: {
    message: string;
  };
}
