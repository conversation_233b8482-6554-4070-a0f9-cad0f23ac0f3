'use client';

import React from 'react';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: any;
}

interface ResumeDetailsSectionProps {
  parsedResume: ParsedResume;
  showEditor: boolean;
}

export default function ResumeDetailsSection({
  parsedResume,
  showEditor,
}: ResumeDetailsSectionProps) {
  if (!showEditor) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* AI-Powered Resume Analysis */}
      {parsedResume.aiParsedResume ? (
        <div className="space-y-6">
          {/* Personal Details */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-medium mb-4 text-indigo-700">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-semibold text-gray-700">Name:</p>
                <p className="text-gray-900">{parsedResume.aiParsedResume.personal_details.name || 'Not detected'}</p>
              </div>
              <div>
                <p className="font-semibold text-gray-700">Location:</p>
                <p className="text-gray-900">{parsedResume.aiParsedResume.personal_details.location || 'Not detected'}</p>
              </div>
              <div>
                <p className="font-semibold text-gray-700">Email:</p>
                <div className="space-y-1">
                  {parsedResume.aiParsedResume.personal_details.email_addresses.length > 0 ? (
                    parsedResume.aiParsedResume.personal_details.email_addresses.map((email: string, index: number) => (
                      <p key={index} className="text-gray-900">{email}</p>
                    ))
                  ) : (
                    <p className="text-gray-500 italic">Not detected</p>
                  )}
                </div>
              </div>
              <div>
                <p className="font-semibold text-gray-700">Phone:</p>
                <div className="space-y-1">
                  {parsedResume.aiParsedResume.personal_details.phone_numbers.length > 0 ? (
                    parsedResume.aiParsedResume.personal_details.phone_numbers.map((phone: string, index: number) => (
                      <p key={index} className="text-gray-900">{phone}</p>
                    ))
                  ) : (
                    <p className="text-gray-500 italic">Not detected</p>
                  )}
                </div>
              </div>
            </div>
            {parsedResume.aiParsedResume.personal_details.profile_urls.length > 0 && (
              <div className="mt-4">
                <p className="font-semibold text-gray-700 mb-2">Profile URLs:</p>
                <div className="space-y-1">
                  {parsedResume.aiParsedResume.personal_details.profile_urls.map((profile: any, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="text-sm bg-gray-100 px-2 py-1 rounded">{profile.type}</span>
                      <a href={profile.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        {profile.url}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Summary/Objective */}
          {parsedResume.aiParsedResume.summary_objective.summary_text && (
            <div className="p-6 bg-white rounded-lg shadow-md">
              <h3 className="text-xl font-medium mb-4 text-green-700">Professional Summary</h3>
              <p className="text-gray-800 leading-relaxed">{parsedResume.aiParsedResume.summary_objective.summary_text}</p>
            </div>
          )}

          {/* Skills */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-medium mb-4 text-blue-700">Skills</h3>
            {parsedResume.aiParsedResume.skills.length > 0 ? (
              <div className="space-y-4">
                {parsedResume.aiParsedResume.skills.map((skillCategory: any, index: number) => (
                  <div key={index}>
                    <h4 className="font-semibold text-gray-700 mb-2">{skillCategory.category}</h4>
                    <div className="flex flex-wrap gap-2">
                      {skillCategory.items.map((skill: any, skillIndex: number) => (
                        <span key={skillIndex} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No skills detected</p>
            )}
          </div>

          {/* Work Experience */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-medium mb-4 text-amber-700">Work Experience</h3>
            {parsedResume.aiParsedResume.work_experience.length > 0 ? (
              <div className="space-y-6">
                {parsedResume.aiParsedResume.work_experience.map((work: any, index: number) => (
                  <div key={index} className="border-l-4 border-amber-200 pl-4">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-900">{work.job_title || 'Position'}</h4>
                        <p className="text-gray-700">{work.company_name || 'Company'}</p>
                      </div>
                      <div className="text-sm text-gray-600 mt-1 md:mt-0">
                        <p>{work.location}</p>
                        <p>{work.start_date} - {work.end_date}</p>
                        {work.duration && <p className="italic">({work.duration})</p>}
                      </div>
                    </div>
                    {work.responsibilities_achievements.length > 0 && (
                      <ul className="list-disc list-inside space-y-1 text-gray-800 text-sm">
                        {work.responsibilities_achievements.map((item: any, itemIndex: number) => (
                          <li key={itemIndex}>{item}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No work experience detected</p>
            )}
          </div>

          {/* Education */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-xl font-medium mb-4 text-purple-700">Education</h3>
            {parsedResume.aiParsedResume.education.length > 0 ? (
              <div className="space-y-4">
                {parsedResume.aiParsedResume.education.map((edu: any, index: number) => (
                  <div key={index} className="border-l-4 border-purple-200 pl-4">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                      <div>
                        <h4 className="font-semibold text-gray-900">{edu.degree || 'Degree'}</h4>
                        {edu.major && <p className="text-gray-700">Major: {edu.major}</p>}
                        <p className="text-gray-700">{edu.institution_name || 'Institution'}</p>
                      </div>
                      <div className="text-sm text-gray-600 mt-1 md:mt-0">
                        <p>{edu.location}</p>
                        <p>{edu.graduation_date}</p>
                        {edu.gpa_score && <p>GPA: {edu.gpa_score}</p>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No education details detected</p>
            )}
          </div>

          {/* Certifications */}
          {parsedResume.aiParsedResume.certifications.length > 0 && (
            <div className="p-6 bg-white rounded-lg shadow-md">
              <h3 className="text-xl font-medium mb-4 text-red-700">Certifications</h3>
              <div className="space-y-3">
                {parsedResume.aiParsedResume.certifications.map((cert: any, index: number) => (
                  <div key={index} className="border-l-4 border-red-200 pl-4">
                    <h4 className="font-semibold text-gray-900">{cert.certification_name}</h4>
                    <p className="text-gray-700">{cert.issuing_body}</p>
                    <div className="text-sm text-gray-600">
                      <p>Issued: {cert.issue_date}</p>
                      {cert.expiration_date && <p>Expires: {cert.expiration_date}</p>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Other Sections */}
          {(parsedResume.aiParsedResume.other_sections.projects.length > 0 ||
            parsedResume.aiParsedResume.other_sections.awards_honors.length > 0 ||
            parsedResume.aiParsedResume.other_sections.publications.length > 0 ||
            parsedResume.aiParsedResume.other_sections.volunteer_experience.length > 0 ||
            parsedResume.aiParsedResume.other_sections.interests.length > 0) && (
              <div className="p-6 bg-white rounded-lg shadow-md">
                <h3 className="text-xl font-medium mb-4 text-gray-700">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {parsedResume.aiParsedResume.other_sections.projects.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Projects</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        {parsedResume.aiParsedResume.other_sections.projects.map((project: any, index: number) => (
                          <li key={index}>{project}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {parsedResume.aiParsedResume.other_sections.awards_honors.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Awards & Honors</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        {parsedResume.aiParsedResume.other_sections.awards_honors.map((award: any, index: number) => (
                          <li key={index}>{award}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {parsedResume.aiParsedResume.other_sections.publications.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Publications</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        {parsedResume.aiParsedResume.other_sections.publications.map((pub: any, index: number) => (
                          <li key={index}>{pub}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {parsedResume.aiParsedResume.other_sections.volunteer_experience.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Volunteer Experience</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        {parsedResume.aiParsedResume.other_sections.volunteer_experience.map((vol: any, index: number) => (
                          <li key={index}>{vol}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {parsedResume.aiParsedResume.other_sections.interests.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Interests</h4>
                      <div className="flex flex-wrap gap-2">
                        {parsedResume.aiParsedResume.other_sections.interests.map((interest: any, index: number) => (
                          <span key={index} className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
                            {interest}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
        </div>
      ) : (
        /* Fallback when no AI parsed resume data is available */
        <div className="p-6 bg-white rounded-lg shadow-md">
          <h3 className="text-xl font-medium mb-4 text-gray-700">Resume Data</h3>
          <p className="text-gray-500 italic">
            Resume parsing is in progress or no structured data is available. 
            Please try refreshing the page or re-uploading your resume.
          </p>
        </div>
      )}
    </div>
  );
}
