import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable strict mode for React
  reactStrictMode: true,
  // Configure server-side environment variables
  env: {
    MONGODB_URI: process.env.MONGODB_URI,
  },

  // Configure external images
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.themeszo.com',
        port: '',
        pathname: '/wp-content/uploads/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Configure webpack to handle MongoDB
  webpack: (config) => {
    // Fixes npm packages that depend on `fs` module
    if (!config.resolve) {
      config.resolve = {};
    }
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },

  // Configure file uploads (moved from api config)
  serverExternalPackages: ['mongoose', 'pdf-parse'],
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb', // Increase body size limit for file uploads
    },
  }
};

export default nextConfig;
