import { getResumeParsingPrompt, ParsedResumeResponse } from './prompt-reader';
import { createAIModelFromEnv, AIModel } from './model-factory';

/**
 * Resume parsing service that uses markdown prompts with LangChain
 */
export class ResumeParserService {
  private model: AIModel;

  constructor() {
    this.model = createAIModelFromEnv();
  }

  /**
   * Parse resume text using AI to extract structured information
   * 
   * @param resumeText The raw text content extracted from a PDF resume
   * @returns Structured resume data as ParsedResumeResponse
   */
  async parseResume(resumeText: string): Promise<ParsedResumeResponse> {
    try {
      // Get the prompt template from the markdown file
      const promptTemplate = getResumeParsingPrompt();

      // Format the prompt with the resume text
      const formattedPrompt = await promptTemplate.format({
        resumeText: resumeText,
      });

      // Call the AI model
      const response = await this.model.invoke(formattedPrompt);

      // Extract and parse JSON content from the response
      const responseText = response.content.toString();
      const parsedResponse = this.parseJsonResponse(responseText);

      // Validate the response structure
      this.validateResponse(parsedResponse);

      return parsedResponse;
    } catch (error) {
      console.error('Error parsing resume with AI:', error);
      throw new Error(`Resume parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse JSON response from AI model, handling various formats
   * 
   * @param responseText Raw response text from AI model
   * @returns Parsed JSON object
   */
  private parseJsonResponse(responseText: string): ParsedResumeResponse {
    try {
      // Try to parse the JSON directly
      return JSON.parse(responseText);
    } catch {
      // If direct parsing fails, try to extract JSON from markdown code blocks
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/) ||
        responseText.match(/```\n([\s\S]*?)\n```/) ||
        responseText.match(/{[\s\S]*?}/);

      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } catch {
          throw new Error('Failed to parse AI response as JSON');
        }
      } else {
        throw new Error('Failed to extract JSON from AI response');
      }
    }
  }

  /**
   * Validate the parsed response structure
   * 
   * @param response Parsed response object
   */
  private validateResponse(response: any): void {
    if (!response || typeof response !== 'object') {
      throw new Error('Invalid response: not an object');
    }

    if (!response.personal_details) {
      throw new Error('Invalid response: missing personal_details field');
    }

    if (!response.summary_objective) {
      throw new Error('Invalid response: missing summary_objective field');
    }

    if (!Array.isArray(response.skills)) {
      throw new Error('Invalid response: skills must be an array');
    }

    if (!Array.isArray(response.work_experience)) {
      throw new Error('Invalid response: work_experience must be an array');
    }

    if (!Array.isArray(response.education)) {
      throw new Error('Invalid response: education must be an array');
    }

    if (!Array.isArray(response.certifications)) {
      throw new Error('Invalid response: certifications must be an array');
    }

    if (!response.other_sections) {
      throw new Error('Invalid response: missing other_sections field');
    }
  }

  /**
   * Generate fallback parsed resume structure when AI parsing fails
   * 
   * @param resumeText The raw resume text
   * @returns Basic ParsedResumeResponse structure
   */
  generateFallbackResponse(resumeText: string): ParsedResumeResponse {
    // Extract basic information using simple text processing
    const lines = resumeText.split('\n').filter(line => line.trim());
    
    // Try to find name (usually first non-empty line)
    const potentialName = lines.find(line => 
      line.length > 2 && 
      line.length < 50 && 
      !line.includes('@') && 
      !line.includes('http')
    ) || null;

    // Try to find email addresses
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emails = resumeText.match(emailRegex) || [];

    // Try to find phone numbers
    const phoneRegex = /(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
    const phones = resumeText.match(phoneRegex) || [];

    return {
      personal_details: {
        name: potentialName || '',
        phone_numbers: phones,
        email_addresses: emails,
        profile_urls: [],
        location: ''
      },
      summary_objective: {
        summary_text: resumeText.substring(0, 200) + '...'
      },
      skills: [{
        category: 'General Skills',
        items: []
      }],
      work_experience: [],
      education: [],
      certifications: [],
      other_sections: {
        awards_honors: [],
        projects: [],
        publications: [],
        volunteer_experience: [],
        interests: [],
        references_available: resumeText.toLowerCase().includes('references available') || 
                             resumeText.toLowerCase().includes('references upon request')
      }
    };
  }
}

/**
 * Convenience function to parse a resume using the service
 * 
 * @param resumeText The raw text content extracted from a PDF resume
 * @returns Structured resume data as ParsedResumeResponse
 */
export async function parseResumeText(resumeText: string): Promise<ParsedResumeResponse> {
  const service = new ResumeParserService();
  
  try {
    return await service.parseResume(resumeText);
  } catch (error) {
    console.warn('AI parsing failed, using fallback:', error);
    return service.generateFallbackResponse(resumeText);
  }
}
