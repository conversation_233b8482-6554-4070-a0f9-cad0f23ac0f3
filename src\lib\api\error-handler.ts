import { NextResponse } from 'next/server';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string | number;
}

/**
 * Handles API errors and returns a standardized error response
 * @param error The error to handle
 * @returns A NextResponse with the appropriate status code and error message
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);
  
  // Default error response
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errorCode = 'server_error';
  
  // Handle known error types
  if (error instanceof Error) {
    const apiError = error as ApiError;
    
    message = apiError.message;
    
    // Handle MongoDB duplicate key error
    if (apiError.code === 11000) {
      statusCode = 400;
      message = 'A record with this information already exists';
      errorCode = 'duplicate_key';
    }
    
    // Handle custom status code if provided
    if (apiError.statusCode) {
      statusCode = apiError.statusCode;
    }
  }
  
  // Return standardized error response
  return NextResponse.json(
    {
      success: false,
      error: {
        message,
        code: errorCode,
      },
    },
    { status: statusCode }
  );
}

/**
 * Creates an API error with a specific status code
 * @param message Error message
 * @param statusCode HTTP status code
 * @returns An ApiError object
 */
export function createApiError(message: string, statusCode: number): ApiError {
  const error = new Error(message) as ApiError;
  error.statusCode = statusCode;
  return error;
}
