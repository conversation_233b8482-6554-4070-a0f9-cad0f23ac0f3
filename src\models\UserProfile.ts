import mongoose, { Schema, Document, Model } from 'mongoose';
import { IUser } from './User';
import { ResumeData } from '@/lib/editor/resume-transformer';
import { ParsedResumeResponse } from '@/lib/ai/prompt-reader';

// Define individual resume interface
export interface IResume {
  _id?: string;
  rawText: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  uploadDate: Date;
  htmlContent?: string;
  pdfBuffer: Buffer;
  // Structured data for resume editor (EditorJS format)
  resumeStructuredData?: ResumeData;
  // Comprehensive AI-parsed resume data
  aiParsedResume?: ParsedResumeResponse;
}

// Define the UserProfile interface
export interface IUserProfile extends Document {
  user: IUser['_id'];
  resumes: IResume[];
  // Note: createdAt and updatedAt are automatically added by timestamps: true
}

// Define the resume sub-schema
const ResumeSchema: Schema = new Schema({
  rawText: {
    type: String,
    required: [true, 'Resume text content is required'],
  },
  fileName: {
    type: String,
    required: [true, 'File name is required'],
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required'],
  },
  mimeType: {
    type: String,
    required: [true, 'MIME type is required'],
  },
  uploadDate: {
    type: Date,
    default: Date.now,
  },
  htmlContent: {
    type: String,
  },
  pdfBuffer: {
    type: Buffer,
    required: [true, 'PDF buffer is required'],
  },
  resumeStructuredData: {
    type: Schema.Types.Mixed,
  },
  aiParsedResume: {
    type: Schema.Types.Mixed,
  },
}, {
  timestamps: true,
});

// Define the UserProfile schema
const UserProfileSchema: Schema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      unique: true,
    },
    resumes: [ResumeSchema],
  },
  {
    timestamps: true,
  }
);

// Create and export the UserProfile model
export default (mongoose.models.UserProfile as Model<IUserProfile>) ||
  mongoose.model<IUserProfile>('UserProfile', UserProfileSchema);
