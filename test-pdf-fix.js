/**
 * Test script to verify the PDF generator connection close fix
 * This script tests multiple concurrent PDF generations to ensure no connection errors occur
 */

const { generatePdfFromHtml, generateResumePdf } = require('./src/lib/pdf/pdf-generator.ts');

// Simple HTML content for testing
const testHtml = `
  <div>
    <h1>Test Resume</h1>
    <h2>Contact Information</h2>
    <p><PERSON></p>
    <p><EMAIL></p>
    <p>(555) 123-4567</p>
    
    <h2>Experience</h2>
    <div class="experience-item">
      <div class="item-header">
        <div>
          <div class="item-title">Software Engineer</div>
          <div class="item-company">Tech Company Inc.</div>
        </div>
        <div class="item-date">2020 - Present</div>
      </div>
      <div class="achievements">
        <ul>
          <li>Developed web applications using React and Node.js</li>
          <li>Improved system performance by 40%</li>
          <li>Led a team of 5 developers</li>
        </ul>
      </div>
    </div>
    
    <h2>Skills</h2>
    <div class="skills-grid">
      <div class="skill-category">
        <h4>Programming Languages</h4>
        <div class="skill-tags">
          <span class="skill-tag">JavaScript</span>
          <span class="skill-tag">TypeScript</span>
          <span class="skill-tag">Python</span>
        </div>
      </div>
    </div>
  </div>
`;

async function testSingleGeneration() {
  console.log('Testing single PDF generation...');
  try {
    const pdfBuffer = await generateResumePdf(testHtml);
    console.log(`✅ Single generation successful - PDF size: ${pdfBuffer.length} bytes`);
    return true;
  } catch (error) {
    console.error('❌ Single generation failed:', error.message);
    return false;
  }
}

async function testConcurrentGenerations() {
  console.log('Testing concurrent PDF generations...');
  const promises = [];
  
  // Create 5 concurrent PDF generation requests
  for (let i = 0; i < 5; i++) {
    promises.push(
      generateResumePdf(testHtml).then(buffer => ({
        success: true,
        size: buffer.length,
        index: i
      })).catch(error => ({
        success: false,
        error: error.message,
        index: i
      }))
    );
  }
  
  try {
    const results = await Promise.all(promises);
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Concurrent test completed:`);
    console.log(`   - Successful: ${successful.length}/5`);
    console.log(`   - Failed: ${failed.length}/5`);
    
    if (failed.length > 0) {
      console.log('❌ Failed generations:');
      failed.forEach(f => {
        console.log(`   - Generation ${f.index}: ${f.error}`);
      });
    }
    
    return failed.length === 0;
  } catch (error) {
    console.error('❌ Concurrent test failed:', error.message);
    return false;
  }
}

async function testSequentialGenerations() {
  console.log('Testing sequential PDF generations...');
  let successCount = 0;
  
  for (let i = 0; i < 3; i++) {
    try {
      const pdfBuffer = await generateResumePdf(testHtml);
      console.log(`✅ Sequential generation ${i + 1} successful - PDF size: ${pdfBuffer.length} bytes`);
      successCount++;
    } catch (error) {
      console.error(`❌ Sequential generation ${i + 1} failed:`, error.message);
    }
  }
  
  console.log(`Sequential test completed: ${successCount}/3 successful`);
  return successCount === 3;
}

async function runAllTests() {
  console.log('🧪 Starting PDF Generator Connection Fix Tests\n');
  
  const results = {
    single: await testSingleGeneration(),
    concurrent: await testConcurrentGenerations(),
    sequential: await testSequentialGenerations()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   - Single Generation: ${results.single ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   - Concurrent Generation: ${results.concurrent ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   - Sequential Generation: ${results.sequential ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 PDF Generator connection close error has been successfully fixed!');
    console.log('The new standalone function approach eliminates browser instance sharing issues.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.');
  }
  
  return allPassed;
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests };
