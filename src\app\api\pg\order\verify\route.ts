import { NextResponse } from "next/server";
import { Cashfree, CFEnvironment } from "cashfree-pg";
import Transaction from "@/models/Transaction";
import User from "@/models/User";
import connectToDatabase from "@/lib/mongodb";
import { withNextAuth } from "@/lib/auth/middleware";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

const { CASHFREE_CLIENT_ID, CASHFREE_CLIENT_SECRET, CASHFREE_ENV } =
	process.env as Record<string, string | undefined>;

const cfEnv =
	(CASHFREE_ENV || "sandbox").toLowerCase() === "production"
		? CFEnvironment.PRODUCTION
		: CFEnvironment.SANDBOX;

export const GET = withNextAuth(async (req, context, authenticatedUser) => {
	try {
		// Connect to database
		await connectToDatabase();
		
		if (!CASHFREE_CLIENT_ID || !CASHFREE_CLIENT_SECRET) {
			return NextResponse.json(
				{
					ok: false,
					error: {
						message:
							"Cashfree credentials are not configured. Set CASHFREE_CLIENT_ID and CASHFREE_CLIENT_SECRET.",
					},
				},
				{ status: 500 }
			);
		}

		const url = new URL(req.url);
		const orderId =
			url.searchParams.get("order_id")

		if (!orderId) {
			return NextResponse.json(
				{ ok: false, error: { message: "Missing required 'order_id' query param" } },
				{ status: 400 }
			);
		}

		// Verify that the transaction belongs to the authenticated user
		const transaction = await Transaction.findOne({ 
			orderId: orderId,
			userId: authenticatedUser.userId 
		});

		if (!transaction) {
			return NextResponse.json(
				{ ok: false, error: { message: "Transaction not found or access denied" } },
				{ status: 404 }
			);
		}

		const cashfree = new Cashfree(
			cfEnv,
			CASHFREE_CLIENT_ID,
			CASHFREE_CLIENT_SECRET
		);

		const cfRes = await cashfree.PGFetchOrder(orderId);
		const data = cfRes?.data ?? null;
		
		// Helper function to update transaction status
		const updateTransactionIfPaid = async (orderData: any) => {
			if (orderData?.order_status === "PAID") {
				try {
					// Find and update the transaction
					const updatedTransaction = await Transaction.findOneAndUpdate(
						{ orderId: orderData.order_id },
						{
							status: "PAID",
							$set: {
								"paymentDetails.cfPaymentId": orderData.cf_order_id,
								"paymentDetails.paymentTime": new Date(orderData.created_at),
							}
						},
						{ new: true }
					);

					// If transaction was updated successfully, also update the user record
					if (updatedTransaction) {
						const transactionAmount = updatedTransaction.amount / 100; // Convert from paise to rupees
						
						// Calculate credits based on amount (example: 1 credit per ₹10, minimum 1 credit)
						const creditsToAdd = Math.max(1, Math.floor(transactionAmount / 10));

						await User.findByIdAndUpdate(
							updatedTransaction.userId,
							{
								$inc: {
									"fineTuneCredits.remainingCredits": creditsToAdd,
								},
								$set: {
									"fineTuneCredits.lastPurchasedCredits": creditsToAdd,
									"fineTuneCredits.lastCreditPurchasedAt": new Date(orderData.created_at),
									"paymentStatus.paidAt": new Date(orderData.created_at),
									"paymentStatus.paymentReference": updatedTransaction._id,
								}
							}
						);
					}
				} catch (transactionError) {
					console.error("Failed to update transaction or user:", transactionError);
					// Continue without breaking the flow
				}
			}
		};

		// Update transaction for initial response
		await updateTransactionIfPaid(data);

		// Poll twice with 5 second interval if order is not paid
		if (data?.order_status !== "PAID") {
			for (let i = 0; i < 2; i++) {
				await new Promise(resolve => setTimeout(resolve, 5000));
				const retryRes = await cashfree.PGFetchOrder(orderId);
				const retryData = retryRes?.data;
				
				if (retryData?.order_status === "PAID") {
					// Update transaction for retry response
					await updateTransactionIfPaid(retryData);
					return NextResponse.json({ ok: true, order: retryData }, { status: 200 });
				}
			}
		}

		return NextResponse.json({ ok: true, order: data }, { status: 200 });
	} catch (err: any) {
		const status = err?.response?.status ?? 500;
		const errorPayload = err?.response?.data ?? {
			message: err?.message || "Unknown error while fetching order",
		};
		return NextResponse.json({ ok: false, error: errorPayload }, { status });
	}
});

