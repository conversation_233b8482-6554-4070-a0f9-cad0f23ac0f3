import { NextRequest, NextResponse } from 'next/server';
import { validateTokenFromRequest, createUnauthorizedResponse, JWTPayload } from './jwt';

// Type for NextAuth user info
export type NextAuthUser = {
  userId: string;
  email: string;
};

// Type for API route handler with authenticated user (NextAuth)
export type NextAuth<PERSON><PERSON>Handler = (
  request: NextRequest,
  context: { params: Promise<any> },
  user: NextAuthUser
) => Promise<NextResponse> | NextResponse;

// Type for API route handler with authenticated user (JWT - legacy)
export type AuthenticatedApiHandler = (
  request: NextRequest,
  context: { params: Promise<any> },
  user: JWTPayload
) => Promise<NextResponse> | NextResponse;

// Type for regular API route handler
export type ApiHandler = (
  request: NextRequest,
  context: { params: Promise<any> }
) => Promise<NextResponse> | NextResponse;

/**
 * Higher-order function that wraps API routes with NextAuth authentication
 * @param handler The API route handler that requires authentication
 * @returns A wrapped handler that validates NextAuth sessions
 */
export function withNextAuth(handler: NextAuthApiHandler): <PERSON><PERSON><PERSON><PERSON><PERSON> {
  return async (request: NextRequest, context: { params: Promise<any> }) => {
    // Get user info from headers set by middleware
    const userId = request.headers.get('x-user-id');
    const email = request.headers.get('x-user-email');
    
    if (!userId || !email) {
      return createUnauthorizedResponse('Authentication required. Please log in.');
    }
    
    const user: NextAuthUser = { userId, email };
    
    // Call the original handler with the authenticated user
    return handler(request, context, user);
  };
}

/**
 * Higher-order function that wraps API routes with JWT authentication (legacy)
 * @param handler The API route handler that requires authentication
 * @returns A wrapped handler that validates JWT tokens
 */
export function withAuth(handler: AuthenticatedApiHandler): ApiHandler {
  return async (request: NextRequest, context: { params: Promise<any> }) => {
    // Validate JWT token from request
    const user = await validateTokenFromRequest(request);
    
    if (!user) {
      return createUnauthorizedResponse('Authentication required. Please provide a valid JWT token.');
    }
    
    // Call the original handler with the authenticated user
    return handler(request, context, user);
  };
}

/**
 * Middleware function to check if user owns the resource
 * @param userId The user ID from the token
 * @param resourceUserId The user ID from the resource (e.g., URL parameter)
 * @returns True if user owns the resource, false otherwise
 */
export function checkResourceOwnership(userId: string, resourceUserId: string): boolean {
  return userId === resourceUserId;
}

/**
 * Higher-order function that wraps API routes with NextAuth authentication and resource ownership check
 * @param handler The API route handler that requires authentication and ownership
 * @param getUserIdFromParams Function to extract user ID from route parameters
 * @returns A wrapped handler that validates NextAuth sessions and resource ownership
 */
export function withAuthAndOwnership(
  handler: NextAuthApiHandler,
  getUserIdFromParams: (params: any) => string
): ApiHandler {
  return async (request: NextRequest, context: { params: Promise<any> }) => {
    // Get user info from headers set by middleware
    const userId = request.headers.get('x-user-id');
    const email = request.headers.get('x-user-email');
    
    if (!userId || !email) {
      return createUnauthorizedResponse('Authentication required. Please log in.');
    }
    
    // Await params since it's a promise
    const resolvedParams = await context.params;
    
    // Extract user ID from route parameters
    const resourceUserId = getUserIdFromParams(resolvedParams);
    
    // Check if user owns the resource
    if (!checkResourceOwnership(userId, resourceUserId)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Forbidden. You can only access your own resources.',
          },
        },
        { status: 403 }
      );
    }
    
    const user: NextAuthUser = { userId, email };
    
    // Call the original handler with the authenticated user
    return handler(request, context, user);
  };
}

/**
 * Higher-order function that wraps API routes with JWT authentication and resource ownership check (legacy)
 * @param handler The API route handler that requires authentication and ownership
 * @param getUserIdFromParams Function to extract user ID from route parameters
 * @returns A wrapped handler that validates JWT tokens and resource ownership
 */
export function withJWTAuthAndOwnership(
  handler: AuthenticatedApiHandler,
  getUserIdFromParams: (params: any) => string
): ApiHandler {
  return async (request: NextRequest, context: { params: Promise<any> }) => {
    // Validate JWT token from request
    const user = await validateTokenFromRequest(request);
    
    if (!user) {
      return createUnauthorizedResponse('Authentication required. Please provide a valid JWT token.');
    }
    
    // Await params since it's a promise
    const resolvedParams = await context.params;
    
    // Extract user ID from route parameters
    const resourceUserId = getUserIdFromParams(resolvedParams);
    
    // Check if user owns the resource
    if (!checkResourceOwnership(user.userId, resourceUserId)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Forbidden. You can only access your own resources.',
          },
        },
        { status: 403 }
      );
    }
    
    // Call the original handler with the authenticated user
    return handler(request, context, user);
  };
}
