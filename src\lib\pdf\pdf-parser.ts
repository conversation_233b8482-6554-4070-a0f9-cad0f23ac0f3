import pdfParse from 'pdf-parse';

/**
 * Interface for parsed resume data
 */
export interface ParsedResume {
  rawText: string;
  skills: string[];
  education: string[];
  experience: string[];
  contact: {
    phone?: string;
    email?: string;
    address?: string;
  };
}

/**
 * Simple PDF parser for resumes
 * Extracts text and attempts to identify key sections
 * @param buffer PDF file buffer
 * @returns Parsed resume data
 */
export async function parseResumePdf(buffer: Buffer): Promise<ParsedResume> {
  try {
    // Parse PDF to extract text
    const pdfData = await pdfParse(buffer);
    const rawText = pdfData.text;
    
    return {
      rawText,
      skills: extractSkills(rawText),
      education: extractEducation(rawText),
      experience: extractExperience(rawText),
      contact: extractContactInfo(rawText),
    };
  } catch (error) {
    console.error('Error parsing PDF:', error);
    throw new Error('Failed to parse PDF document');
  }
}

/**
 * Extract skills from resume text
 * This is a simple implementation that looks for common skill section headers
 * and extracts text that might represent skills
 * @param text Raw text from PDF
 * @returns Array of potential skills
 */
function extractSkills(text: string): string[] {
  // Look for skills section
  const skillsRegex = /(?:SKILLS|TECHNICAL SKILLS|CORE COMPETENCIES|EXPERTISE)(?:\s*:|\s*\n)([\s\S]*?)(?:\n\s*\n|\n\s*[A-Z])/i;
  const match = text.match(skillsRegex);
  
  if (match && match[1]) {
    // Extract skills from the matched section
    // Split by commas, bullets, or new lines
    return match[1]
      .split(/[,•\n]/)
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0 && skill.length < 50); // Filter out empty or too long strings
  }
  
  // Fallback: look for common technical terms
  const technicalTerms = [
    'JavaScript', 'TypeScript', 'Python', 'Java', 'C#', 'C++', 'Ruby', 'PHP',
    'HTML', 'CSS', 'React', 'Angular', 'Vue', 'Node.js', 'Express', 'Django',
    'Flask', 'Spring', 'ASP.NET', 'SQL', 'NoSQL', 'MongoDB', 'PostgreSQL',
    'MySQL', 'Oracle', 'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes',
    'Git', 'CI/CD', 'Jenkins', 'Travis CI', 'Agile', 'Scrum', 'Kanban'
  ];
  
  return technicalTerms.filter(term => 
    new RegExp(`\\b${term}\\b`, 'i').test(text)
  );
}

/**
 * Extract education information from resume text
 * @param text Raw text from PDF
 * @returns Array of education entries
 */
function extractEducation(text: string): string[] {
  // Look for education section
  const educationRegex = /(?:EDUCATION|ACADEMIC|QUALIFICATIONS)(?:\s*:|\s*\n)([\s\S]*?)(?:\n\s*\n|\n\s*[A-Z])/i;
  const match = text.match(educationRegex);
  
  if (match && match[1]) {
    // Split education section by new lines and filter empty lines
    return match[1]
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  }
  
  // Fallback: look for degree keywords
  const degreeKeywords = [
    'Bachelor', 'Master', 'PhD', 'Doctorate', 'BSc', 'BA', 'MSc', 'MA', 'MBA',
    'University', 'College', 'School', 'Institute', 'Degree'
  ];
  
  const educationLines = text.split('\n')
    .filter(line => 
      degreeKeywords.some(keyword => 
        new RegExp(`\\b${keyword}\\b`, 'i').test(line)
      )
    )
    .map(line => line.trim());
  
  return [...new Set(educationLines)]; // Remove duplicates
}

/**
 * Extract work experience information from resume text
 * @param text Raw text from PDF
 * @returns Array of experience entries
 */
function extractExperience(text: string): string[] {
  // Look for experience section
  const experienceRegex = /(?:EXPERIENCE|WORK EXPERIENCE|EMPLOYMENT|PROFESSIONAL EXPERIENCE)(?:\s*:|\s*\n)([\s\S]*?)(?:\n\s*\n|\n\s*[A-Z])/i;
  const match = text.match(experienceRegex);
  
  if (match && match[1]) {
    // Split experience section by new lines and filter empty lines
    return match[1]
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  }
  
  // Fallback: look for date patterns that might indicate job entries
  const datePattern = /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\s+(-|to|–|—)\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}|\d{4}\s+(-|to|–|—)\s+\d{4}|\d{4}\s+(-|to|–|—)\s+(Present|Current|Now)\b/i;
  
  const experienceLines = text.split('\n')
    .filter(line => datePattern.test(line))
    .map(line => line.trim());
  
  return [...new Set(experienceLines)]; // Remove duplicates
}

/**
 * Extract contact information from resume text
 * @param text Raw text from PDF
 * @returns Contact information object
 */
function extractContactInfo(text: string): { phone?: string; email?: string; address?: string } {
  const contact: { phone?: string; email?: string; address?: string } = {};
  
  // Extract email
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const emailMatch = text.match(emailRegex);
  if (emailMatch) {
    contact.email = emailMatch[0];
  }
  
  // Extract phone number (various formats)
  const phoneRegex = /\b(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b/;
  const phoneMatch = text.match(phoneRegex);
  if (phoneMatch) {
    contact.phone = phoneMatch[0];
  }
  
  // Extract address (this is more challenging and less reliable)
  // Look for postal code patterns or address keywords
  const addressLines = text.split('\n')
    .filter(line => 
      /\b[A-Z]{2}\s+\d{5}(-\d{4})?\b/.test(line) || // US postal code
      /\b[A-Z][0-9][A-Z]\s+[0-9][A-Z][0-9]\b/.test(line) || // Canadian postal code
      /\b(Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Plaza|Plz|Square|Sq)\b/i.test(line)
    )
    .map(line => line.trim());
  
  if (addressLines.length > 0) {
    contact.address = addressLines[0]; // Take the first matching line as the address
  }
  
  return contact;
}
