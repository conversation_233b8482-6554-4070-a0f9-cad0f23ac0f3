import { NextRequest, NextResponse } from 'next/server';
import { ChatService } from '@/lib/ai/chat-service';
import { handleApiError } from '@/lib/api/error-handler';
import { withAuthAndOwnership } from '@/lib/auth/middleware';

export const POST = withAuthAndOwnership(
  async (request: NextRequest, context, _authenticatedUser) => {
    try {
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          { success: false, error: { message: 'Invalid parameters' } },
          { status: 400 }
        );
      }
      const { sessionId } = params;
      const body = await request.json();
      const { message } = body;

      // Validate required fields
      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        return NextResponse.json(
          { success: false, error: { message: 'Message content is required' } },
          { status: 400 }
        );
      }

      // Create chat service instance
      const chatService = new ChatService();

      // Process the message
      const response = await chatService.processMessage(sessionId, message.trim());

      return NextResponse.json({
        success: true,
        data: {
          response: response.response,
          isComplete: response.isComplete,
          metadata: {
            finalResume: response.finalResume,
            changes: response.changes,
          },
        },
      });

    } catch (error) {
      console.error('Error processing chat message:', error);
      return handleApiError(error);
    }
  },
  (params) => params.id
);
