'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { ProfileIcon } from '@/components/ProfileIcon';

export const AppHeader = () => {
  const { data: session } = useSession();

  return (
    <header className="w-full bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/">
            <Image src="/logos/logo1.png" alt="PaydAI" width={128} height={32} />
          </Link>
        </div>
        
        <div className="flex items-center space-x-4">
          {session && <ProfileIcon />}
        </div>
      </div>
    </header>
  );
};
