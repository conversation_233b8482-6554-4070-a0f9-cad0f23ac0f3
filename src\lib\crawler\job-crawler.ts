import axios from 'axios';
import * as cheerio from 'cheerio';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { createAIModelFromEnv } from '../ai/model-factory';

/**
 * Extracts job description text from a URL using AI
 * @param url The URL to crawl for job description
 * @returns The extracted job description text
 */
export async function crawlJobDescription(url: string): Promise<string> {
  try {
    // Validate URL format
    if (!isValidUrl(url)) {
      throw new Error('Invalid URL format');
    }

    // Fetch the HTML content from the URL
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      timeout: 10000, // 10 seconds timeout
    });

    // Parse the HTML content to get the text
    const $ = cheerio.load(response.data);

    // Remove scripts, styles, and other non-content elements
    $('script, style, noscript, svg, header, footer, nav, aside').remove();

    // Get the page title
    const pageTitle = $('title').text().trim();

    // Get the page text content
    const pageText = $('body').text()
      .replace(/\s+/g, ' ')
      .trim();

    // Limit the text to a reasonable size to avoid token limits
    const limitedText = pageText.substring(0, 10000);

    // Use AI to extract the job description
    return await extractJobDescriptionWithAI(url, pageTitle, limitedText);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout: The website took too long to respond');
      }
      if (error.response) {
        throw new Error(`Failed to fetch URL: HTTP status ${error.response.status}`);
      }
      throw new Error(`Failed to fetch URL: ${error.message}`);
    }
    throw error;
  }
}

/**
 * Uses AI to extract job description from webpage content
 * @param url The URL of the job posting
 * @param pageTitle The title of the webpage
 * @param pageContent The text content of the webpage
 * @returns The extracted job description
 */
async function extractJobDescriptionWithAI(url: string, pageTitle: string, pageContent: string): Promise<string> {
  // Initialize the LLM using the model factory
  const model = createAIModelFromEnv();

  // Create a prompt template
  const promptTemplate = PromptTemplate.fromTemplate(`
    You are an expert at extracting job descriptions from web pages. Your task is to analyze the content 
    of a job posting page and extract only the job description part. Focus on identifying the main responsibilities, 
    requirements, qualifications, and other details that describe the job.

    URL: {url}
    Page Title: {pageTitle}
    
    Page Content:
    {pageContent}

    Extract and return ONLY the job description text. Do not include any other parts of the page like 
    navigation, headers, footers, or application instructions. Format the output as clean text.
    
    Job Description:
  `);

  // Create a parser
  const parser = new StringOutputParser();

  // Create a chain
  const chain = RunnableSequence.from([
    promptTemplate,
    model,
    parser,
  ]);

  // Run the chain
  const result = await chain.invoke({
    url,
    pageTitle,
    pageContent,
  });

  // Clean up the result
  const cleanedResult = result.trim();

  if (!cleanedResult) {
    throw new Error('Could not extract job description from the provided URL');
  }
  return cleanedResult;
}

/**
 * Validates if a string is a properly formatted URL
 * @param url The URL string to validate
 * @returns Boolean indicating if the URL is valid
 */
function isValidUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol);
  } catch {
    return false;
  }
}
