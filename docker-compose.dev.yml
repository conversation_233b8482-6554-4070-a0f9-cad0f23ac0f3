version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: mongodb
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=paydai
    networks:
      - app-network

  # Next.js application service in development mode
  nextjs-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: nextjs-app
    restart: always
    ports:
      - "3050:3050"
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/paydai
      - NODE_ENV=development
    volumes:
      # Mount everything except package-lock.json
      - ./src:/app/src
      - ./public:/app/public
      - ./tsconfig.json:/app/tsconfig.json
      - ./package.json:/app/package.json
      - ./next.config.ts:/app/next.config.ts
      - ./postcss.config.mjs:/app/postcss.config.mjs
      - ./eslint.config.mjs:/app/eslint.config.mjs
      - ./.env.local:/app/.env.local
      - ./.gitignore:/app/.gitignore
      - ./README.md:/app/README.md
      - /app/.next
      - /app/node_modules
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge
