import { useState } from 'react';
import { MatchResult } from '@/types/match';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: any;
}

export const useResumeEditor = () => {
  const [showEditor, setShowEditor] = useState(false);
  const [fineTuneChanges, setFineTuneChanges] = useState<string[]>([]);
  const [editorContent, setEditorContent] = useState<string>();
  const [showChat, setShowChat] = useState(false);

  // Handle fine-tuning the resume for a job description - now shows chat interface
  const handleFineTuneResume = (
    parsedResume: ParsedResume | null,
    matchResult: MatchResult | null,
    existingUser: string | null,
    setError: (error: string) => void
  ) => {
    if (!parsedResume || !matchResult || !existingUser) {
      setError('Resume data and match result are required for fine-tuning');
      return false;
    }

    // Clear any previous errors
    setError('');

    // Show the chat interface
    setShowChat(true);
    return true;
  };

  // Handle chat completion
  const handleChatComplete = (finalResult: { htmlContent: string; changes: string[] }) => {
    // Update the editor content with the fine-tuned HTML
    setEditorContent(finalResult.htmlContent);

    // Store the changes made during fine-tuning
    setFineTuneChanges(finalResult.changes);

    // Close the chat interface
    setShowChat(false);

    // Make sure the editor is visible
    setShowEditor(true);
  };

  // Handle chat close
  const handleChatClose = () => {
    setShowChat(false);
  };

  // Toggle editor visibility
  const toggleEditor = () => {
    setShowEditor(!showEditor);
  };

  // Reset editor state
  const resetEditor = () => {
    setShowEditor(false);
    setEditorContent('');
    setFineTuneChanges([]);
    setShowChat(false);
  };

  return {
    showEditor,
    fineTuneChanges,
    editorContent,
    showChat,
    handleFineTuneResume,
    handleChatComplete,
    handleChatClose,
    toggleEditor,
    resetEditor,
    setShowEditor,
  };
};
