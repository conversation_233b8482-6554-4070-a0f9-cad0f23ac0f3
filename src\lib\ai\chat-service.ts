import connectToDatabase from '@/lib/mongodb';
import ChatMessage from '@/models/ChatMessage';
import ChatSession from '@/models/ChatSession';
import { IChatMessage, IChatSession, ResumeFineTuneContext, ResumeFineTuneMetadata } from '@/types/chat';
import { MatchResult } from '@/types/match';
import { AIMessage, BaseMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";
import { PromptTemplate } from "@langchain/core/prompts";
import { BufferMemory } from "langchain/memory";
import { ObjectId } from 'mongodb';
import { createAIModelFromEnv, AIModel } from './model-factory';

export class ChatService {
  private model: AIModel;
  private memory: BufferMemory;

  constructor() {
    // Initialize the AI model using the factory
    this.model = createAIModelFromEnv();

    // Initialize memory
    this.memory = new BufferMemory({
      returnMessages: true,
      memoryKey: "chat_history",
    });
  }

  /**
   * Create a new chat session for resume fine-tuning using MatchResult
   */
  async createResumeFineTuneSession(
    userId: string,
    resumeRawText: string,
    matchResult: MatchResult
  ): Promise<{ sessionId: string; firstQuestion: string; analysis: string }> {
    await connectToDatabase();

    // Create the session with MatchResult data
    const session = new ChatSession({
      userId: new ObjectId(userId),
      sessionType: 'resume_finetune',
      status: 'active',
      context: {
        resumeText: resumeRawText,
        jobDescription: matchResult.jobDescription || '',
        originalResume: resumeRawText, // backup
        matchResult,
      },
      metadata: {
        currentQuestionIndex: 0,
        matchScore: matchResult.score,
        identifiedGaps: matchResult.feedback.gaps,
        strengths: matchResult.feedback.strengths,
        recommendations: matchResult.feedback.recommendations,
        conversationComplete: false,
      },
    });

    const savedSession = await session.save();

    // Generate the first question based on MatchResult
    const firstQuestion = await this.generateFirstQuestion(matchResult, resumeRawText);

    // Save the system message and first question
    await this.saveMessage(savedSession._id.toString(), 'system',
      `Starting resume fine-tuning session. Match Score: ${matchResult.score}%. Analysis: ${matchResult.feedback.overall_assessment}`, 'analysis');

    await this.saveMessage(savedSession._id.toString(), 'assistant', firstQuestion, 'question');

    return {
      sessionId: savedSession._id.toString(),
      firstQuestion,
      analysis: matchResult.feedback.overall_assessment,
    };
  }

  /**
   * Process a user message and generate AI response
   */
  async processMessage(
    sessionId: string,
    userMessage: string
  ): Promise<{ response: string; isComplete: boolean; finalResume?: string; changes?: string[] }> {
    await connectToDatabase();

    // Get the session
    const session = await ChatSession.findById(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    // Save the user message
    await this.saveMessage(sessionId, 'user', userMessage, 'answer');

    // Load conversation history into memory
    await this.loadConversationHistory(sessionId);

    // Use single AI prompt to decide next action and generate response
    const nextAction = await this.generateNextAction(session, userMessage);

    if (nextAction.action === 'COMPLETE') {
      // Time to finalize
      const finalResult = await this.generateFinalResume(session);

      // Update session status
      await ChatSession.findByIdAndUpdate(sessionId, {
        status: 'completed',
        'metadata.finalResult': finalResult,
        'metadata.conversationComplete': true,
      });

      // Save the final response
      await this.saveMessage(sessionId, 'assistant', nextAction.response, 'final_result');

      return {
        response: nextAction.response,
        isComplete: true,
        finalResume: finalResult.htmlContent,
        changes: finalResult.changes,
      };
    } else {
      // Continue with next question
      const metadata = session.metadata as ResumeFineTuneMetadata;
      const currentQuestionIndex = metadata.currentQuestionIndex || 0;
      await ChatSession.findByIdAndUpdate(sessionId, {
        'metadata.currentQuestionIndex': currentQuestionIndex + 1,
      });

      // Save the AI response
      await this.saveMessage(sessionId, 'assistant', nextAction.response, 'question');

      return {
        response: nextAction.response,
        isComplete: false,
      };
    }
  }

  /**
   * Get chat history for a session
   */
  async getChatHistory(sessionId: string): Promise<{ session: IChatSession; messages: IChatMessage[] }> {
    await connectToDatabase();

    const session = await ChatSession.findById(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const messages = await ChatMessage.find({ sessionId: new ObjectId(sessionId) })
      .sort({ createdAt: 1 });

    return {
      session: session.toObject(),
      messages: messages.map(msg => msg.toObject()),
    };
  }

  /**
   * Generate the first question based on MatchResult
   */
  private async generateFirstQuestion(matchResult: MatchResult, resumeRawText: string): Promise<string> {
    const promptTemplate = PromptTemplate.fromTemplate(`
      You are a professional resume coach helping someone improve their resume for a job application.
      
      Based on the resume analysis results and the actual resume content, generate the first conversational question to help improve their resume.

      Resume Content:
      {resumeContent}

      Match Score: {score}%
      Overall Assessment: {assessment}
      
      Key Gaps Identified:
      {gaps}
      
      Strengths:
      {strengths}
      
      Recommendations:
      {recommendations}

      Generate a specific, conversational question that addresses the most important gap or improvement area.
      Keep it friendly and focused on gathering specific information that will help improve their resume.
      Reference specific sections or content from their resume to make the question more personalized.
      
      Start with something like "I noticed..." or "Looking at your resume..." to make it conversational.
    `);

    const prompt = await promptTemplate.format({
      resumeContent: resumeRawText,
      score: matchResult.score,
      assessment: matchResult.feedback.overall_assessment,
      gaps: matchResult.feedback.gaps.join('\n- '),
      strengths: matchResult.feedback.strengths.join('\n- '),
      recommendations: matchResult.feedback.recommendations.join('\n- '),
    });

    const response = await this.model.invoke(prompt);
    return response.content.toString();
  }

  /**
   * Generate next action (continue with question or complete) using single AI prompt
   */
  private async generateNextAction(session: IChatSession, userResponse: string): Promise<{ action: 'CONTINUE' | 'COMPLETE'; response: string }> {
    const context = session.context as ResumeFineTuneContext;
    const metadata = session.metadata as ResumeFineTuneMetadata;
    const matchResult = context.matchResult;
    const currentQuestionIndex = metadata.currentQuestionIndex || 0;

    // Get the full conversation history
    const messages = await ChatMessage.find({
      sessionId: session._id
    }).sort({ createdAt: 1 });

    // Format conversation history (exclude system messages)
    const conversationHistory = messages
      .filter(msg => msg.role !== 'system')
      .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)
      .join('\n\n');

    const promptTemplate = PromptTemplate.fromTemplate(`
      You are a professional resume coach. Based on the conversation so far, decide whether to ask another question or complete the resume optimization.

      Original Match Score: {score}%
      Identified Gaps: {gaps}
      Current question index: {questionIndex}

      Full Conversation History:
      {conversationHistory}

      User's latest response: {userResponse}

      Analyze the conversation and decide:
      1. CONTINUE: If you need more specific information to optimize the resume effectively
      2. COMPLETE: If you have gathered enough information for meaningful improvements

      Consider these criteria:
      - Have major gaps been addressed with specific information?
      - Has the user provided quantifiable achievements and metrics?
      - Are there still important areas needing clarification?
      - Has the conversation been productive (detailed responses)?
      - Have we gathered enough for 2-3 major improvements?
      - Don't exceed 8 questions unless absolutely necessary
      - If user seems disengaged (short responses), consider completing

      Respond in this exact JSON format:
      {{
        "action": "CONTINUE" or "COMPLETE",
        "response": "Your response text here"
      }}

      If action is CONTINUE: Generate the next conversational question that builds on what's already been discussed and addresses remaining gaps.
      If action is COMPLETE: Provide a friendly completion message thanking them for their responses.

      Important: Don't repeat topics already covered. Build on the conversation history.
    `);

    const prompt = await promptTemplate.format({
      score: matchResult.score,
      gaps: matchResult.feedback.gaps.join(', '),
      questionIndex: currentQuestionIndex,
      conversationHistory: conversationHistory || 'No previous conversation',
      userResponse,
    });

    try {
      const response = await this.model.invoke(prompt);
      let responseText = response.content.toString();

      // Remove markdown code blocks and clean up the response
      responseText = responseText.replace(/```json\n?/g, '');
      responseText = responseText.replace(/```\n?/g, '');
      responseText = responseText.replace(/`/g, ''); // Remove any remaining backticks
      responseText = responseText.trim();

      // Try to extract JSON if it's embedded in other text
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        responseText = jsonMatch[0];
      }

      const result = JSON.parse(responseText);

      // Safety check: force completion after 8 questions
      if (currentQuestionIndex >= 8) {
        return {
          action: 'COMPLETE',
          response: 'Thank you for your detailed responses! I have enough information to optimize your resume.'
        };
      }

      return {
        action: result.action === 'COMPLETE' ? 'COMPLETE' : 'CONTINUE',
        response: result.response
      };
    } catch (error) {
      console.error('Error in generateNextAction:', error);
      // Fallback: complete after 5 questions if AI decision fails
      if (currentQuestionIndex >= 5) {
        return {
          action: 'COMPLETE',
          response: 'Thank you for your responses! I have enough information to optimize your resume.'
        };
      } else {
        return {
          action: 'CONTINUE',
          response: 'Could you tell me more about any specific achievements or projects you\'d like to highlight?'
        };
      }
    }
  }

  /**
   * Generate the final optimized resume
   */
  private async generateFinalResume(session: IChatSession) {
    // Get all user responses from the conversation
    const messages = await ChatMessage.find({
      sessionId: session._id,
      role: 'user'
    }).sort({ createdAt: 1 });
    
    const userResponses = messages.map(msg => msg.content).join('\n\n');
    const context = session.context as ResumeFineTuneContext;
    const matchResult = context.matchResult;

    if (!context.resumeText) {
      console.error('ERROR: context.resumeText is undefined or empty');
    }

    const promptTemplate = PromptTemplate.fromTemplate(`
      Based on the conversation with the user, optimize their resume for the job.

      Original Resume: {originalResume}
      Job Description: {jobDescription}
      Original Match Score: {matchScore}%
      Identified Gaps: {gaps}
      User's responses to questions: {userResponses}

      Generate an updated resume in well-formatted HTML that addresses the gaps and incorporates the user's additional information.
      
      IMPORTANT HTML FORMATTING REQUIREMENTS:
      1. Create a complete, valid HTML document with proper structure
      2. Include CSS styling for professional appearance
      3. Use semantic HTML elements (h1, h2, h3, p, ul, li, etc.)
      4. Ensure all HTML tags are properly closed
      5. Use proper indentation and formatting
      6. Include contact information, summary, skills, experience, and education sections
      7. Make the resume visually appealing and easy to read

      Focus on content improvements:
      1. Adding quantifiable achievements mentioned by the user
      2. Highlighting relevant skills and experience
      3. Improving keyword alignment with the job description
      4. Better formatting and presentation
      5. Professional styling and layout

      Return ONLY a JSON object with this exact structure:
      {{
        "htmlContent": "<!DOCTYPE html><html><head><title>Resume</title><style>/* CSS styles here */</style></head><body><!-- Resume content here --></body></html>",
        "changes": ["Specific change 1", "Specific change 2", "etc."]
      }}

      The htmlContent must be a complete, valid HTML document that can be displayed in an iframe.
    `);

    const prompt = await promptTemplate.format({
      originalResume: context.resumeText,
      jobDescription: context.jobDescription,
      matchScore: matchResult.score,
      gaps: matchResult.feedback.gaps.join(', '),
      userResponses,
    });
    
    const response = await this.model.invoke(prompt);

    try {
      let responseText = response.content.toString();

      // Clean up the response
      responseText = responseText.replace(/```json\s*/gi, '');
      responseText = responseText.replace(/```\s*/gi, '');
      responseText = responseText.trim();
      // write to /tmp/out.json
      // // Try to extract JSON from the response
      // const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      // if (jsonMatch) {
      //   responseText = jsonMatch[0];
      // }

      const result = JSON.parse(responseText);

      // Validate that we have the required fields
      if (!result.htmlContent || !result.changes) {
        throw new Error('Invalid response structure');
      }

      // Sanitize and validate the HTML content
      let htmlContent = result.htmlContent;
      
      // Ensure it's a complete HTML document
      if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html>')) {
        htmlContent = this.wrapInCompleteHtml(htmlContent);
      }

      // Clean up any remaining markdown artifacts
      htmlContent = htmlContent.replace(/```html\s*/gi, '');
      htmlContent = htmlContent.replace(/```\s*/gi, '');

      return {
        htmlContent: htmlContent,
        changes: Array.isArray(result.changes) ? result.changes : ['Resume optimized based on conversation'],
      };

    } catch (error) {
      console.error('Error in generateFinalResume:', error);
      console.error('Raw response:', response.content.toString());
      
      // Fallback: create a basic HTML resume from the original text
      const fallbackHtml = this.createFallbackHtml(context.resumeText);
      
      return {
        htmlContent: fallbackHtml,
        changes: ['Resume optimized based on conversation and match analysis (fallback mode)'],
      };
    }
  }

  /**
   * Wrap content in a complete HTML document
   */
  private wrapInCompleteHtml(content: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 2.2em;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            font-size: 1.4em;
        }
        h3 {
            color: #34495e;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        p {
            margin-bottom: 12px;
            text-align: justify;
        }
        ul, ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }
        li {
            margin-bottom: 6px;
        }
        .contact-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
        }
        .section {
            margin-bottom: 30px;
        }
        .job-entry {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .job-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        .company {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 5px;
        }
        .date {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        .skill-tag {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid #bbdefb;
        }
        .education-entry {
            margin-bottom: 15px;
            padding-bottom: 10px;
        }
        .degree {
            font-weight: bold;
            color: #2c3e50;
        }
        .institution {
            color: #7f8c8d;
            font-style: italic;
        }
        strong {
            color: #2c3e50;
        }
        @media print {
            body { padding: 0; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    ${content}
</body>
</html>`;
  }

  /**
   * Create fallback HTML from plain text
   */
  private createFallbackHtml(text: string): string {
    const lines = text.split('\n').filter(line => line.trim());
    let content = '<div class="resume-content">';
    
    if (lines.length === 0) {
      content += '<p>No resume content available.</p>';
    } else {
      content += '<h1>Resume</h1>';
      lines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed) {
          // Simple heuristic for headings vs content
          if (trimmed.length < 60 && !trimmed.includes('.') && !trimmed.includes(',')) {
            content += `<h3>${trimmed}</h3>`;
          } else {
            content += `<p>${trimmed}</p>`;
          }
        }
      });
    }
    
    content += '</div>';
    return this.wrapInCompleteHtml(content);
  }


  /**
   * Save a message to the database
   */
  private async saveMessage(sessionId: string, role: string, content: string, messageType: string) {
    const message = new ChatMessage({
      sessionId: new ObjectId(sessionId),
      role,
      content,
      messageType,
      createdAt: new Date(),
    });

    await message.save();
  }

  /**
   * Load conversation history into LangChain memory
   */
  private async loadConversationHistory(sessionId: string) {
    const messages = await ChatMessage.find({ sessionId: new ObjectId(sessionId) })
      .sort({ createdAt: 1 });

    // Clear existing memory
    this.memory.clear();

    // Convert to LangChain message format
    const langchainMessages: BaseMessage[] = messages.map(msg => {
      switch (msg.role) {
        case 'user':
          return new HumanMessage(msg.content);
        case 'assistant':
          return new AIMessage(msg.content);
        case 'system':
          return new SystemMessage(msg.content);
        default:
          return new AIMessage(msg.content);
      }
    });

    // Add messages to memory
    for (const message of langchainMessages) {
      await this.memory.chatHistory.addMessage(message);
    }
  }
}
