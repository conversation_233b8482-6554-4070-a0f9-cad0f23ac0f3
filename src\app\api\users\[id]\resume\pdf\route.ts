import { handleApiError } from '@/lib/api/error-handler';
import connectToDatabase from '@/lib/mongodb';
import { withAuthAndOwnership } from '@/lib/auth/middleware';
import User from '@/models/User';
import UserProfile from '@/models/UserProfile';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET handler for serving the PDF file
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response with the PDF file
 */
export const GET = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Connect to the database
      await connectToDatabase();

      // Check if user exists
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Find the user profile
      const userProfile = await UserProfile.findOne({ user: userId });
      if (!userProfile || !userProfile.resumes || userProfile.resumes.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Resume not found for this user',
              code: 'resume_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Get the latest resume (last one added)
      const latestResume = userProfile.resumes[userProfile.resumes.length - 1];
      console.log("**** resume length", userProfile.resumes.length)

      // Return the PDF buffer
      return new NextResponse(latestResume.pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `inline; filename="${latestResume.fileName}"`,
          'Content-Length': latestResume.fileSize.toString(),
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  },
  (params) => params.id
);
