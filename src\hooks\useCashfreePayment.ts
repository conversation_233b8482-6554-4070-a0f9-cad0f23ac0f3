"use client";

import { useCallback, useMemo, useState } from "react";

export type CashfreeUser = {
  id: string;
  email: string;
  phone?: string;
};

export type StartPaymentParams = {
  amount: number | string;
  user: CashfreeUser;
};

export type PaymentOutcome = {
  success: boolean;
  paymentStatus?: string;
  result?: any;
  error?: Error;
};

export function useCashfreePayment() {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  const mode = useMemo(() => {
    const envVal = (
      process.env.CASHFREE_ENV ||
      process.env.NEXT_PUBLIC_CASHFREE_ENV ||
      "sandbox"
    ).toLowerCase();
    return envVal === "production" ? "production" : "sandbox";
  }, []);

  const startPayment = useCallback(
    async ({ amount, user, }: StartPaymentParams): Promise<PaymentOutcome> => {
      try {
        setLoading(true);
        setError(null);
        setStatus("Creating order...");

        const res = await fetch("/api/pg/order", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            order_amount: String(amount),
            customer_details: {
              customer_id: user.id,
              customer_email: user.email,
              customer_phone: user.phone,
            }
          }),
        });

        const json = await res.json();

        if (!res.ok || !json?.ok) {
          throw new Error(
            json?.error?.message || json?.error || `Failed to create order (${res.status})`
          );
        }

        console.log("Create order response:", json);

        const order = json.order || {};
        const paymentSessionId = order?.payment_session_id;
        if (!paymentSessionId) {
          throw new Error("payment_session_id missing from create order response");
        }
        const orderId = order?.order_id;

        setStatus("Loading checkout...");

        const { load } = await import("@cashfreepayments/cashfree-js");
        const cashfree = await load({ mode: mode as "sandbox" | "production" });

        const checkoutOptions = {
          paymentSessionId,
          redirectTarget: "_modal" as const,
        };
        const result: any = await cashfree.checkout(checkoutOptions);

        if (result?.error) {
          console.log(
            "User has closed the popup or there is some payment error, Check for Payment Status"
          );
          console.log(result.error);
          setStatus("Payment cancelled or failed");
          return { 
            success: false, 
            paymentStatus: "CANCELLED_OR_FAILED",
            result,
            error: new Error(result.error.message || "Payment was cancelled or failed")
          };
        }

        if (result?.redirect) {
          console.log("Payment will be redirected");
          setStatus("Payment redirecting...");
          return { 
            success: true, 
            paymentStatus: "REDIRECTING",
            result 
          };
        }

        if (result?.paymentDetails) {
          console.log("Payment has been completed, Check for Payment Status");
          console.log(result.paymentDetails.paymentMessage);
          setStatus("Payment completed, verifying...");
          
          let verificationStatus = "PENDING";
          // Verify order status on server for source-of-truth
          if (orderId) {
            try {
              const verifyRes = await fetch(
                `/api/pg/order/verify?order_id=${encodeURIComponent(orderId)}`
              );
              const verifyJson = await verifyRes.json();
              console.log("Order verification response:", verifyJson);
              verificationStatus = verifyJson?.order_status ;// "PAID", "PENDING", etc.
            } catch (e) {
              console.error("Order verification call failed:", e);
              verificationStatus = "ERROR";
            }
          }
          
          setStatus("Payment completed");
          return { 
            success: true, 
            paymentStatus: verificationStatus,
            result: {
              ...result,
              verificationStatus
            }
          };
        }

        // Fallback for unexpected result
        console.log("Unexpected checkout result:", result);
        setStatus("Payment completed with unknown status");
        return { 
          success: true, 
          paymentStatus: "UNKNOWN",
          result 
        };
        
      } catch (e: any) {
        console.error("Payment error:", e);
        const msg = e?.message || "Something went wrong while processing payment.";
        setError(msg);
        setStatus("Payment failed");
        return { 
          success: false, 
          paymentStatus: "error",
          error: e 
        };
      } finally {
        setLoading(false);
      }
    },
    [mode]
  );

  const reset = useCallback(() => {
    setLoading(false);
    setStatus("");
    setError(null);
  }, []);

  return { loading, status, error, startPayment, reset };
}
