'use client';

import React, { cloneElement } from 'react';
import { SearchIcon, FileTextIcon, PenToolIcon, SendIcon } from 'lucide-react';
import Link from 'next/link';

export const HowItWorks = () => {
  const steps = [
    {
      icon: <SearchIcon className="h-12 w-12 text-blue-600" />,
      title: 'Find Matching Jobs',
      description: 'Our smart algorithm finds jobs that match your skills, experience, and preferences to focus your search on opportunities where you have the highest chance of success.',
      isComingSoon: false
    },
    {
      icon: <FileTextIcon className="h-12 w-12 text-blue-600" />,
      title: 'Customize Your Resume',
      description: 'Get AI-powered suggestions to tailor your resume for each job application, highlighting the most relevant skills and experiences for the specific role.',
      isComingSoon: false
    },
    {
      icon: <PenToolIcon className="h-12 w-12 text-blue-600" />,
      title: 'Create Perfect Cover Letters',
      description: "Generate personalized cover letters that address the job requirements and showcase why you're the ideal candidate for the position.",
      isComingSoon: true
    },
    {
      icon: <SendIcon className="h-12 w-12 text-blue-600" />,
      title: 'Apply with Confidence',
      description: 'Submit your applications with perfectly tailored materials that significantly increase your chances of landing an interview.',
      isComingSoon: false
    }
  ];

  return (
    <section id="how-it-works" style={{ background: 'white', color: 'black' }} className="py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            How PaydAI Works
          </h2>
          <p className="text-xl max-w-3xl mx-auto">
            Our streamlined process helps you find the right opportunities and
            create tailored application materials to maximize your chances of
            success.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="rounded p-6 border hover:border-black transition-colors" style={{ background: 'white', color: 'black', borderColor: 'black' }}>
              <div className="flex justify-center mb-6">
                <div className="h-12 w-12" style={{ color: 'black' }}>
                  {step.icon}
                </div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-center">
                {step.title}
              </h3>
              <p className="text-center">{step.description}</p>
              {step.isComingSoon && <p className="text-center text-blue-500 mt-2 text-sm">(coming soon)</p>}
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <Link href="/app" className="px-8 py-3 rounded text-lg font-medium inline-block" style={{ background: 'black', color: 'white' }}>
            Try It For Free
          </Link>
        </div>
      </div>
    </section>
  );
};
