import { Chat<PERSON>penAI } from '@langchain/openai';
import { ChatDeepSeek } from '@langchain/deepseek';
import { ChatZhipuAI } from '@langchain/community/chat_models/zhipuai';

export enum AIProvider {
  OPENAI = 'openai',
  DEEPSEEK = 'deepseek',
  ZHIPUAI = 'zhipuai'
}

export type AIModel = ChatOpenAI | ChatDeepSeek | ChatZhipuAI;

export interface ModelConfig {
  apiKey: string;
  modelName: string;
  temperature: number;
}

/**
 * Factory function to create AI model instances based on provider
 * @param provider The AI provider to use
 * @param config Configuration options for the model
 * @returns Configured AI model instance
 */
export function createAIModel(provider: AIProvider, config: ModelConfig): AIModel {
  if (!config.apiKey) {
    throw new Error('API key is required in model configuration');
  }

  switch (provider) {
    case AIProvider.DEEPSEEK:
      return new ChatDeepSeek(config);
    
    case AIProvider.ZHIPUAI:
      return new ChatZhipuAI(config);
    
    case AIProvider.OPENAI:
      return new ChatOpenAI(config);
    
    default:
      throw new Error(`Unsupported AI provider: ${provider}`);
  }
}

/**
 * Get the AI provider from environment or default to OpenAI
 * @returns The current AI provider
 */
export function getAIProviderFromEnv(): AIProvider {
  const envProvider = process.env.AI_PROVIDER?.toLowerCase();
  
  switch (envProvider) {
    case 'deepseek':
      return AIProvider.DEEPSEEK;
    case 'zhipuai':
      return AIProvider.ZHIPUAI;
    case 'openai':
    default:
      return AIProvider.OPENAI;
  }
}

/**
 * Get the model configuration from environment variables
 * @returns Model configuration object
 */
export function getModelConfigFromEnv(): ModelConfig {
  const apiKey = process.env.AI_API_KEY;
  if (!apiKey) {
    throw new Error('AI_API_KEY environment variable is required');
  }

  return {
    apiKey,
    modelName: process.env.LLM_MODEL_NAME || 'gpt-3.5-turbo',
    temperature: 0.1
  };
}

/**
 * Create AI model using environment configuration
 * @returns Configured AI model instance
 */
export function createAIModelFromEnv(): AIModel {
  const provider = getAIProviderFromEnv();
  const config = getModelConfigFromEnv();
  return createAIModel(provider, config);
}
