import LoadingSpinner from './LoadingSpinner';
import MatchResults from './MatchResults';
import CreditDisplay from './CreditDisplay';
import PaymentModal from './PaymentModal';
import React, { useState } from 'react';

interface JobMatchingSectionProps {
  inputType: 'text' | 'url';
  jobDescription: string;
  jobUrl: string;
  matchLoading: boolean;
  matchResult: any; // Replace with proper type
  error: string | null;
  creditsData: any; // Replace with proper type
  creditsLoading: boolean;
  creditsError: string | null;
  onInputTypeChange: (type: 'text' | 'url') => void;
  onJobDescriptionChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onJobUrlChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onMatch: () => void;
  onFineTune: () => void;
  onBuyCredits: () => void;
}

export default function JobMatchingSection({
  inputType,
  jobDescription,
  jobUrl,
  matchLoading,
  matchResult,
  error,
  creditsData,
  creditsLoading,
  creditsError,
  onInputTypeChange,
  onJobDescriptionChange,
  onJobUrlChange,
  onMatch,
  onFineTune,
  onBuyCredits
}: JobMatchingSectionProps) {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  const handleBuyCredits = () => {
    setIsPaymentModalOpen(true);
  };

  const handlePaymentSuccess = () => {
    // Optionally refresh credits data or show success message
    onBuyCredits(); // Call the original function to refresh data
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    // You could add a toast notification here
  };
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-medium text-indigo-700">Job Description Matching</h3>
      </div>

      {error && (
        <div className="mb-6 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="mb-4">
        <div className="flex space-x-4 mb-4">
          <button
            type="button"
            onClick={() => onInputTypeChange('text')}
            className={`flex-1 py-2 px-4 rounded-md ${
              inputType === 'text'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Paste Job Description
          </button>
          <button
            type="button"
            onClick={() => onInputTypeChange('url')}
            className={`flex-1 py-2 px-4 rounded-md ${
              inputType === 'url'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Enter Job URL
          </button>
        </div>

        {inputType === 'text' ? (
          <>
            <label htmlFor="jobDescription" className="block text-gray-700 mb-2 font-medium">
              Paste Job Description
            </label>
            <textarea
              id="jobDescription"
              value={jobDescription}
              onChange={onJobDescriptionChange}
              placeholder="Paste the job description here to match with your resume..."
              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[150px]"
            />
          </>
        ) : (
          <>
            <label htmlFor="jobUrl" className="block text-gray-700 mb-2 font-medium">
              Enter Job Posting URL
            </label>
            <input
              type="url"
              id="jobUrl"
              value={jobUrl}
              onChange={onJobUrlChange}
              placeholder="https://example.com/job-posting"
              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="mt-2 text-sm text-gray-500">
              Enter the URL of a job posting, and we&apos;ll extract the job description automatically.
            </p>
          </>
        )}
      </div>

      <button
        onClick={onMatch}
        disabled={
          matchLoading ||
          (inputType === 'text' && !jobDescription.trim()) ||
          (inputType === 'url' && !jobUrl.trim())
        }
        className={`w-full py-3 px-4 rounded text-white font-medium mb-4 ${
          matchLoading ||
          (inputType === 'text' && !jobDescription.trim()) ||
          (inputType === 'url' && !jobUrl.trim())
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-indigo-600 hover:bg-indigo-700 transition-colors'
        }`}
      >
        {matchLoading ? (
          <div className="flex items-center justify-center">
            <LoadingSpinner size="small" />
            <span className="ml-2">Deeply analysing fitment</span>
          </div>
        ) : (
          'Match with Resume'
        )}
      </button>

      {matchResult && (
        <div className="mt-4">
          <MatchResults matchResult={matchResult} />
          
          {creditsData && (
            <div className="mt-4 flex justify-center">
              <CreditDisplay
                remainingCredits={creditsData.remainingCredits}
                totalUsed={creditsData.totalUsed}
                isFirstTime={creditsData.isFirstTime}
                loading={creditsLoading}
                error={creditsError || undefined}
              />
            </div>
          )}
          
          {creditsData && creditsData.remainingCredits <= 0 ? (
            <button
              onClick={handleBuyCredits}
              className="mt-6 w-full py-3 px-4 rounded text-white font-medium transition-colors bg-blue-500 hover:bg-blue-600"
            >
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span>Buy More Credits</span>
              </div>
            </button>
          ) : (
            <button
              onClick={onFineTune}
              disabled={creditsData ? creditsData.remainingCredits <= 0 : false}
              className="mt-6 w-full py-3 px-4 rounded text-white font-medium transition-colors bg-green-500 hover:bg-green-600"
            >
              Fine tune resume for this job
            </button>
          )}
        </div>
      )}
      
      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
        amount={99}
      />
    </div>
  );
}
