import { useState, useEffect } from 'react';
import { useNextAuth } from './useNextAuth';
import { useResumeUpload } from './useResumeUpload';
import { useJobMatching } from './useJobMatching';
import { useResumeEditor } from './useResumeEditor';
import { useResumeDownload } from './useResumeDownload';
import { useFineTuneCredits } from './useFineTuneCredits';

export function useAppPageLogic() {
  const [wantsNewUpload, setWantsNewUpload] = useState(false);

  const authHook = useNextAuth();
  const resumeHook = useResumeUpload();
  const jobMatchingHook = useJobMatching();
  const editorHook = useResumeEditor();
  const downloadHook = useResumeDownload();
  const creditsHook = useFineTuneCredits(authHook.existingUser);

  // Extract values for cleaner dependency array
  const { isUserAuthenticated, existingUser, loadUserResume, setError } = authHook;
  const { parsedResume, setParsedResume, handleResumeUpload, handleReset, generateHtmlContent } = resumeHook;

  // Load user's resume on authentication
  useEffect(() => {
    const loadResume = async () => {
      if (isUserAuthenticated && existingUser && !parsedResume && !wantsNewUpload) {
        const resumeData = await loadUserResume(existingUser);
        if (resumeData) {
          setParsedResume(resumeData);
        }
      }
    };

    loadResume();
  }, [isUserAuthenticated, existingUser, loadUserResume, setParsedResume, parsedResume, wantsNewUpload]);

  // Enhanced handlers
  const handleEnhancedResumeUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await handleResumeUpload(
      existingUser, 
      (userId: string) => {
        // This would need to be handled by the auth hook
        // For now, we'll handle it in the component
      }, 
      setError
    );
    
    if (success) {
      setWantsNewUpload(false);
    }
  };

  const handleEnhancedMatch = async () => {
    if (!parsedResume) {
      setError('Resume data is required for matching');
      return;
    }
    await jobMatchingHook.handleMatch(existingUser, setError);
  };

  const handleEnhancedFineTune = async () => {
    if (creditsHook.creditsData && creditsHook.creditsData.remainingCredits <= 0) {
      setError('You have no remaining credits for fine-tuning. Please purchase more credits to continue.');
      return;
    }

    const success = await editorHook.handleFineTuneResume(
      parsedResume, 
      jobMatchingHook.matchResult, 
      existingUser, 
      setError
    );
    
    if (success) {
      await creditsHook.refetchCredits();
    }
  };

  const handleBuyMoreCredits = async () => {
    // Refresh credits data after payment
    await creditsHook.refetchCredits();
  };

  const handleEnhancedReset = () => {
    setWantsNewUpload(true);
    handleReset();
    jobMatchingHook.resetJobMatching();
    editorHook.resetEditor();
    setError('');
  };

  const getHtmlContent = () => {
    return generateHtmlContent(editorHook.editorContent);
  };

  return {
    // State
    wantsNewUpload,
    
    // Hooks
    authHook,
    resumeHook,
    jobMatchingHook,
    editorHook,
    downloadHook,
    creditsHook,
    
    // Handlers
    handleEnhancedResumeUpload,
    handleEnhancedMatch,
    handleEnhancedFineTune,
    handleBuyMoreCredits,
    handleEnhancedReset,
    getHtmlContent,
  };
}
