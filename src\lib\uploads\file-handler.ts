import { NextRequest } from 'next/server';
import path from 'path';
import { createApiError } from '@/lib/api/error-handler';

/**
 * Interface for uploaded file information
 */
export interface UploadedFile {
  buffer: Buffer;
  filename: string;
  mimetype: string;
  size: number;
}

/**
 * Parses a multipart form request and extracts file data using FormData API
 * @param req The Next.js request object
 * @returns Promise resolving to the uploaded file information
 */
export async function parseFileUpload(req: NextRequest): Promise<UploadedFile> {
  try {
    // Parse the request as FormData
    const formData = await req.formData();
    
    // Get the file from the 'file' field
    const file = formData.get('file') as File | null;
    
    if (!file) {
      throw createApiError('No file uploaded', 400);
    }
    
    // Get file details
    const filename = file.name;
    const mimetype = file.type;
    const size = file.size;
    
    // Check file type
    if (mimetype !== 'application/pdf') {
      throw createApiError('Only PDF files are accepted', 400);
    }
    
    // Check file size (max 10MB)
    if (size > 10 * 1024 * 1024) {
      throw createApiError('File size exceeds the 10MB limit', 400);
    }
    
    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    return {
      buffer,
      filename,
      mimetype,
      size
    };
  } catch (error) {
    if (error instanceof Error && 'code' in error) {
      // If it's already an API error, rethrow it
      throw error;
    }
    throw createApiError(`Error parsing file upload: ${(error as Error).message}`, 400);
  }
}

/**
 * Validates that the uploaded file is a PDF
 * @param file The uploaded file information
 * @returns True if valid, throws an error if invalid
 */
export function validatePdfFile(file: UploadedFile): boolean {
  // Check file type
  if (file.mimetype !== 'application/pdf') {
    throw createApiError('Only PDF files are accepted', 400);
  }
  
  // Check file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    throw createApiError('File size exceeds the 10MB limit', 400);
  }
  
  // Check file extension
  const ext = path.extname(file.filename).toLowerCase();
  if (ext !== '.pdf') {
    throw createApiError('File must have a .pdf extension', 400);
  }
  
  return true;
}
