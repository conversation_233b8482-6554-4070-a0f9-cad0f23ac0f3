/**
 * Validates an email address
 * @param email The email address to validate
 * @returns True if the email is valid, false otherwise
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates a password
 * @param password The password to validate
 * @returns An object with isValid and message properties
 */
export function validatePassword(password: string): { isValid: boolean; message?: string } {
  if (!password) {
    return { isValid: false, message: 'Password is required' };
  }
  
  if (password.length < 8) {
    return { isValid: false, message: 'Password must be at least 8 characters long' };
  }
  
  // Check for at least one number
  if (!/\d/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one number' };
  }
  
  return { isValid: true };
}

/**
 * Validates user input
 * @param data The user data to validate
 * @returns An object with isValid and errors properties
 */
export function validateUserInput(data: { 
  name?: string; 
  email?: string; 
  password?: string;
}): { 
  isValid: boolean; 
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};
  
  // Validate name
  if (!data.name) {
    errors.name = 'Name is required';
  } else if (data.name.length > 60) {
    errors.name = 'Name cannot be more than 60 characters';
  }
  
  // Validate email
  if (!data.email) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  // Validate password
  if (data.password) {
    const passwordValidation = validatePassword(data.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.message || 'Invalid password';
    }
  } else {
    errors.password = 'Password is required';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}
