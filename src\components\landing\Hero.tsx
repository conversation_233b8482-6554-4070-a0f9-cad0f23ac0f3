'use client';

import React from 'react';
import { CheckCircleIcon } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export const Hero = () => {
  return (
    <section style={{ background: 'white', color: 'black' }} className="py-20 px-4">
      <div className="container mx-auto max-w-6xl flex flex-col md:flex-row items-center">
        <div className="md:w-1/2 md:pr-12 mb-10 md:mb-0">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Tired of your dream job getting lost in the bot maze?
          </h1>
          <p className="text-xl mb-2">
            PaydA<PERSON> flips the script with advanced GenAI that beats the bots at their own game.
            No more playing keyword lottery or wondering if your application disappeared into the void. With PaydAI:
          </p>
          <div className="space-y-3 mb-8">
            <div className="flex items-start">
              <CheckCircleIcon className="mr-2 h-6 w-6 mt-0.5 flex-shrink-0" style={{ color: 'black' }} />
              <p>
                Find jobs that match your skills and experience
              </p>
            </div>
            <div className="flex items-start">
              <CheckCircleIcon className="mr-2 h-6 w-6 mt-0.5 flex-shrink-0" style={{ color: 'black' }} />
              <p>
                Tailor your resume with AI-powered suggestions
              </p>
            </div>
            <div className="flex items-start">
              <CheckCircleIcon className="mr-2 h-6 w-6 mt-0.5 flex-shrink-0" style={{ color: 'black' }} />
              <p>
                Create personalized cover letters in minutes <span className="text-blue-500 text-sm">(coming Soon)</span>
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link href="/app" className="px-8 py-3 rounded text-lg font-medium text-center" style={{ background: 'black', color: 'white' }}>
              Start Free Trial
            </Link>
            {/* <button className="border px-8 py-3 rounded text-lg font-medium" style={{ color: 'black', borderColor: 'black' }}>
              Watch Demo
            </button> */}
          </div>
        </div>
        <div className="md:w-1/2">
          <Image 
            src="https://www.themeszo.com/wp-content/uploads/2021/04/EPBlog.jpg" 
            alt="Interview Success - PaydAI" 
            className="rounded-lg shadow-xl w-full" 
            width={600}
            height={400}
          />
        </div>
      </div>
    </section>
  );
};
