import mongoose from 'mongoose';
import connectToDatabase from '../lib/mongodb';
import User from '../models/User';

// Sample user data
const users = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123', // Contains a number as required by validation
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'secure456', // Contains a number as required by validation
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'testing789', // Contains a number as required by validation
  },
];

async function seedDatabase() {
  try {
    // Connect to the database
    await connectToDatabase();
    console.log('Connected to MongoDB');

    // Clear existing users
    await User.deleteMany({});
    console.log('Cleared existing users');

    // We don't need to manually hash passwords here since the User model
    // will automatically hash passwords via the pre-save hook
    
    // Insert sample users
    const createdUsers = await User.create(users);
    console.log(`Seeded ${createdUsers.length} users`);

    // Log the created users (without passwords)
    console.log('Created users:');
    createdUsers.forEach((user) => {
      console.log(`- ${user.name} (${user.email})`);
    });

    // Disconnect from the database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seedDatabase();
