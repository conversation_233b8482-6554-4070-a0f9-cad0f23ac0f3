import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useCashfreePayment } from '@/hooks/useCashfreePayment';
import { useNextAuth } from '@/hooks/useNextAuth';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess?: () => void;
  onPaymentError?: (error: string) => void;
  amount: number | string;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  onPaymentSuccess,
  onPaymentError,
  amount
}) => {
  const [mobileNumber, setMobileNumber] = useState('');
  const [error, setError] = useState('');
  const { startPayment, loading, status, error: paymentError } = useCashfreePayment();
  const { existingUser, session } = useNextAuth();

  const validateMobileNumber = (mobile: string): boolean => {
    const mobileRegex = /^[6-9]\d{9}$/; // Indian mobile number format
    return mobileRegex.test(mobile);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!mobileNumber.trim()) {
      setError('Mobile number is required');
      return;
    }

    if (!validateMobileNumber(mobileNumber)) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }

    if (!existingUser || !session?.user?.email) {
      setError('User authentication required. Please log in and try again.');
      return;
    }

    try {
      const result = await startPayment({
        amount,
        user: {
          id: existingUser,
          email: session.user.email,
          phone: mobileNumber,
        },
      });

      if (result.success) {
        onPaymentSuccess?.();
        onClose();
        setMobileNumber(''); // Reset form
      } else {
        const errorMsg = result.error?.message || 'Payment failed. Please try again.';
        setError(errorMsg);
        onPaymentError?.(errorMsg);
      }
    } catch (err) {
      const errorMsg = 'An unexpected error occurred. Please try again.';
      setError(errorMsg);
      onPaymentError?.(errorMsg);
    }
  };

  const handleMobileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    if (value.length <= 10) {
      setMobileNumber(value);
      setError('');
    }
  };

  const handleClose = () => {
    if (!loading) {
      setMobileNumber('');
      setError('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black opacity-80 z-50"></div>
      
      {/* Modal */}
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Complete Payment</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          <div className="mb-6">
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-gray-900 mb-2">Payment Details</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span className="font-medium">₹{amount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Email:</span>
                  <span className="font-medium">{session?.user?.email || 'Not available'}</span>
                </div>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
                Mobile Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                id="mobile"
                value={mobileNumber}
                onChange={handleMobileChange}
                placeholder="Enter 10-digit mobile number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
                maxLength={10}
              />
              {(error || paymentError) && (
                <p className="mt-1 text-sm text-red-600">{error || paymentError}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                We&apos;ll use this number for payment verification and updates
              </p>
            </div>

            {status && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">{status}</p>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={loading || !mobileNumber.trim()}
              >
                {loading ? 'Processing...' : 'Proceed to Pay'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    </>
  );
};

export default PaymentModal;