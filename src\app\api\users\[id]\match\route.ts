import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';
import UserProfile from '@/models/UserProfile';
import { handleApiError } from '@/lib/api/error-handler';
import mongoose from 'mongoose';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { crawlJobDescription } from '@/lib/crawler/job-crawler';
import { MatchResult, isValidMatchResult } from '@/types/match';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createAIModelFromEnv } from '@/lib/ai/model-factory';

/**
 * POST handler for matching a resume with a job description
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response with the match score and feedback
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate user ID format
    const { id: userId } = await params
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Invalid user ID format',
            code: 'invalid_id',
          },
        },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { jobDescription, jobUrl, inputType } = body;

    // Validate input based on inputType
    if (inputType === 'text' && (!jobDescription || typeof jobDescription !== 'string' || jobDescription.trim() === '')) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Job description is required',
            code: 'missing_job_description',
          },
        },
        { status: 400 }
      );
    }

    if (inputType === 'url' && (!jobUrl || typeof jobUrl !== 'string' || jobUrl.trim() === '')) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Job URL is required',
            code: 'missing_job_url',
          },
        },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'User not found',
            code: 'user_not_found',
          },
        },
        { status: 404 }
      );
    }

    // Find the user profile with resume data
    const userProfile = await UserProfile.findOne({ user: userId });
    if (!userProfile || !userProfile.resumes || userProfile.resumes.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Resume not found for this user',
            code: 'resume_not_found',
          },
        },
        { status: 404 }
      );
    }

    // Get the latest resume (last one added)
    const latestResume = userProfile.resumes[userProfile.resumes.length - 1];
    if (!latestResume?.rawText) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Resume data not found for this user',
            code: 'resume_data_not_found',
          },
        },
        { status: 404 }
      );
    }

    // Get the resume raw text from the latest resume
    const resumeText = latestResume.rawText;

    // Variable to store the job description (either from text input or crawled from URL)
    let extractedJobDescription = '';

    try {

      // If URL is provided, crawl the job description
      if (inputType === 'url') {
        try {
          extractedJobDescription = await crawlJobDescription(jobUrl);
        } catch (crawlError) {
          return NextResponse.json(
            {
              success: false,
              error: {
                message: `Failed to extract job description from URL: ${crawlError instanceof Error ? crawlError.message : 'Unknown error'}`,
                code: 'crawl_error',
              },
            },
            { status: 400 }
          );
        }
      } else {
        // Use the provided job description text
        extractedJobDescription = jobDescription;
      }

      // Get the match result using LangChain
      const matchResult = await getMatchResult(resumeText, extractedJobDescription);

      // Include the job description in the response
      return NextResponse.json({
        success: true,
        data: {
          ...matchResult,
          jobDescription: extractedJobDescription
        },
      });
    } catch (apiError) {
      console.error('LLM API error:', apiError);

      // Fallback to a simulated match if the API call fails
      console.log('Using fallback matching algorithm');

      // Simple keyword matching as fallback
      const resumeKeywords = extractKeywords(resumeText);
      // Use the original job description if extraction failed
      const jobKeywords = extractKeywords(inputType === 'text' ? jobDescription : '');

      // Calculate simple match score based on keyword overlap
      const matchScore = calculateMatchScore(resumeKeywords, jobKeywords);

      // Create fallback match result with new structure
      const fallbackResult = createFallbackMatchResult(
        matchScore,
        'This is a simulated match score based on keyword analysis. The actual AI-powered matching service is currently unavailable.'
      );

      return NextResponse.json({
        success: true,
        data: {
          ...fallbackResult,
          jobDescription: inputType === 'text' ? jobDescription : 'Job description could not be extracted from URL.'
        },
      });
    }
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get match result using LangChain with C.R.A.F.T. framework
 * @param resumeText The resume text
 * @param jobDescription The job description
 * @returns A detailed match result with comprehensive analysis
 */
async function getMatchResult(resumeText: string, jobDescription: string): Promise<MatchResult> {
  // Initialize the LLM using the model factory
  const model = createAIModelFromEnv();

  // Load the C.R.A.F.T. framework prompt
  const craftPrompt = getCraftPrompt();

  // Create the full prompt by combining the C.R.A.F.T. framework with the inputs
  const fullPrompt = `${craftPrompt}

Resume:
${resumeText}

Job Description:
${jobDescription}

Please provide your analysis in the exact JSON format specified in the prompt above.`;

  // Create a parser
  const parser = new StringOutputParser();

  // Create a chain without using PromptTemplate to avoid curly brace parsing issues
  const chain = RunnableSequence.from([
    model,
    parser,
  ]);

  // Run the chain directly with the full prompt
  console.log("Invoking chain...");
  const result = await chain.invoke([{ role: "user", content: fullPrompt }]);
  try {

  // Parse the result
    // Clean the result by removing markdown code blocks if present
    let cleanedResult = result.trim();

    // Remove markdown code blocks (```json ... ``` or ``` ... ```)
    if (cleanedResult.startsWith('```')) {
      // Find the first newline after ```json or ```
      const firstNewline = cleanedResult.indexOf('\n');
      if (firstNewline !== -1) {
        cleanedResult = cleanedResult.substring(firstNewline + 1);
      }

      // Remove the closing ```
      const lastBackticks = cleanedResult.lastIndexOf('```');
      if (lastBackticks !== -1) {
        cleanedResult = cleanedResult.substring(0, lastBackticks);
      }
    }

    cleanedResult = cleanedResult.trim();

    const parsedResult = JSON.parse(cleanedResult);

    // Validate the result structure
    if (isValidMatchResult(parsedResult)) {
      return parsedResult;
    } else {
      console.warn('Invalid match result structure, using fallback');
      return createFallbackMatchResult(50, 'AI response did not match expected format');
    }
  } catch (error) {
    console.error('Error parsing LLM response:', error);

    // Try to extract JSON from the response using regex as a more robust fallback
    const jsonMatch = result.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        const parsedResult = JSON.parse(jsonMatch[0]);
        if (isValidMatchResult(parsedResult)) {
          return parsedResult;
        }
      } catch (regexError) {
        console.error('Error parsing extracted JSON:', regexError);
      }
    }

    // Try to extract basic score as final fallback
    const scoreMatch = result.match(/score["\s:]+(\d+)/i);
    const score = scoreMatch ? Number(scoreMatch[1]) : 50;

    return createFallbackMatchResult(score, 'Unable to parse detailed analysis from AI response');
  }
}

/**
 * Get the C.R.A.F.T. framework prompt from the markdown file
 */
function getCraftPrompt(): string {
  try {
    const promptPath = join(process.cwd(), 'src', 'prompts', 'resume-job-match.md');
    return readFileSync(promptPath, 'utf-8');
  } catch (error) {
    console.error('Error reading C.R.A.F.T. prompt file:', error);
    // Fallback to a basic prompt if file cannot be read
    return `
      You are a Senior Talent Acquisition Strategist. Analyze the resume and job description provided.
      
      Provide your analysis in this exact JSON format:
      {
        "score": [NUMBER 0-100],
        "feedback": {
          "overall_assessment": "[2-3 sentence summary]",
          "strengths": ["strength 1", "strength 2", "strength 3"],
          "gaps": ["gap 1", "gap 2", "gap 3"],
          "recommendations": ["recommendation 1", "recommendation 2", "recommendation 3"],
          "score_breakdown": {
            "technical_skills": [SCORE 0-100],
            "experience_relevance": [SCORE 0-100],
            "educational_qualifications": [SCORE 0-100],
            "soft_skills_cultural_fit": [SCORE 0-100],
            "achievements_impact": [SCORE 0-100]
          },
          "ats_optimization_score": [SCORE 0-100],
          "interview_likelihood": "[High/Medium/Low with rationale]"
        }
      }
    `;
  }
}

/**
 * Create a fallback match result when AI parsing fails
 */
function createFallbackMatchResult(score: number, message: string): MatchResult {
  return {
    score,
    feedback: {
      overall_assessment: message,
      strengths: ['Unable to analyze strengths due to parsing error'],
      gaps: ['Unable to analyze gaps due to parsing error'],
      recommendations: ['Please try again or contact support'],
      score_breakdown: {
        technical_skills: score,
        experience_relevance: score,
        educational_qualifications: score,
        soft_skills_cultural_fit: score,
        achievements_impact: score
      },
      ats_optimization_score: score,
      interview_likelihood: 'Unable to determine due to parsing error'
    }
  };
}

/**
 * Extract keywords from text
 * @param text The text to extract keywords from
 * @returns An array of keywords
 */
function extractKeywords(text: string): string[] {
  // Convert to lowercase and remove special characters
  const cleanText = text.toLowerCase().replace(/[^\w\s]/g, ' ');

  // Split into words
  const words = cleanText.split(/\s+/);

  // Filter out common words and short words
  const commonWords = new Set([
    'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with',
    'by', 'about', 'as', 'of', 'from', 'is', 'are', 'was', 'were', 'be', 'been',
    'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall',
    'should', 'can', 'could', 'may', 'might', 'must', 'i', 'you', 'he', 'she',
    'it', 'we', 'they', 'this', 'that', 'these', 'those'
  ]);

  const keywords = words.filter(word =>
    word.length > 2 && !commonWords.has(word)
  );

  // Return unique keywords
  return [...new Set(keywords)];
}

/**
 * Calculate a match score based on keyword overlap
 * @param resumeKeywords Keywords from the resume
 * @param jobKeywords Keywords from the job description
 * @returns A match score from 0-100
 */
function calculateMatchScore(resumeKeywords: string[], jobKeywords: string[]): number {
  // Count matching keywords
  const matchingKeywords = resumeKeywords.filter(keyword =>
    jobKeywords.includes(keyword)
  );

  // Calculate score based on percentage of job keywords matched
  // with a minimum score of 30 and maximum of 90 for the fallback algorithm
  const rawScore = (matchingKeywords.length / jobKeywords.length) * 100;
  const adjustedScore = Math.min(90, Math.max(30, rawScore));

  // Round to nearest integer
  return Math.round(adjustedScore);
}
