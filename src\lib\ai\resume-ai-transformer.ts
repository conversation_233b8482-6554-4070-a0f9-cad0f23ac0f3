import { ParsedResume } from '../pdf/pdf-parser';
import { PromptTemplate } from "@langchain/core/prompts";
import { createAIModelFromEnv } from './model-factory';

/**
 * Interface for fine-tuning response
 */
export interface FineTuneResponse {
  htmlContent: string;
  changes: string[];
}

/**
 * Transforms raw resume text into structured HTML content
 * This service uses AI capabilities to generate well-formatted HTML
 * 
 * @param rawText The raw text content of the resume
 * @returns HTML content for the editor
 */
export async function transformResumeTextToHtml(rawText: string): Promise<string> {
  try {
    // Use AI to transform the resume text to HTML
    return await transformResumeTextToHtmlWithAI(rawText);
  } catch (error) {
    console.error('Error transforming resume to HTML with AI:', error);

    // Fallback to rule-based transformation if AI fails
    // Create a minimal ParsedResume object for the fallback function
    const minimalParsedResume: ParsedResume = {
      rawText,
      skills: [],
      education: [],
      experience: [],
      contact: {}
    };
    return generateFallbackHtml(minimalParsedResume);
  }
}

/**
 * Use LangChain to call an AI service to transform resume text to HTML
 * This function takes raw text and uses AI to break it down into structured sections
 */
async function transformResumeTextToHtmlWithAI(rawText: string): Promise<string> {
  try {
    // Initialize the AI model using the factory
    const model = createAIModelFromEnv();

    // Create a prompt template for resume formatting
    const promptTemplate = PromptTemplate.fromTemplate(
      `You are a professional resume formatter. Convert the following resume text into well-structured HTML.
      
      First, analyze the raw text and break it down into these key sections:
      1. Contact Information (name, email, phone, address, LinkedIn, etc.)
      2. Professional Summary or Objective
      3. Skills (technical skills, soft skills, certifications, etc.)
      4. Work Experience (job titles, companies, dates, responsibilities, achievements)
      5. Education (degrees, institutions, dates, relevant coursework)
      6. Additional sections (projects, publications, volunteer work, etc.)
      
      Then, format the content professionally using proper HTML tags:
      - Use h1 for the name
      - Use h2 for section headings
      - Use p for paragraphs
      - Use ul/li for lists
      - Use strong for emphasis
      - Use appropriate div and span elements for layout
      
      Resume Text:
      {resumeText}
      
      Return only the HTML content without any explanations.`
    );

    // Format the prompt with the resume text
    const formattedPrompt = await promptTemplate.format({
      resumeText: rawText,
    });

    // Call the AI model
    const response = await model.invoke(formattedPrompt);

    // Extract HTML content from the response
    let htmlContent = response.content.toString();

    // Remove markdown code blocks if present
    htmlContent = htmlContent.replace(/```html/g, '');
    htmlContent = htmlContent.replace(/```/g, '');

    // Return the cleaned HTML content
    return htmlContent.trim();
  } catch (error) {
    console.error('Error calling AI service:', error);
    throw error; // Rethrow to trigger the fallback
  }
}

/**
 * Generate fallback HTML content using rule-based transformation
 * This is used when the AI service fails
 */
function generateFallbackHtml(parsedResume: ParsedResume): string {
  const { rawText, skills, education, experience, contact } = parsedResume;

  // Create a basic HTML structure
  let htmlContent = '<h1>Resume</h1>';

  // Add a summary section based on the raw text
  htmlContent += '<h2>Summary</h2>';

  // Extract a summary from the first 200 characters of raw text
  const summaryText = rawText.substring(0, 200).trim();
  htmlContent += `<p>${summaryText}...</p>`;

  // Add skills section
  if (skills.length > 0) {
    htmlContent += '<h2>Skills</h2><ul>';
    skills.forEach((skill: string) => {
      htmlContent += `<li>${skill}</li>`;
    });
    htmlContent += '</ul>';
  }

  // Add experience section
  if (experience.length > 0) {
    htmlContent += '<h2>Experience</h2>';
    experience.forEach((exp: string) => {
      htmlContent += `<p>${exp}</p>`;
    });
  }

  // Add education section
  if (education.length > 0) {
    htmlContent += '<h2>Education</h2>';
    education.forEach((edu: string) => {
      htmlContent += `<p>${edu}</p>`;
    });
  }

  // Add contact information
  if (contact) {
    htmlContent += '<h2>Contact Information</h2>';
    if (contact.email) {
      htmlContent += `<p>Email: ${contact.email}</p>`;
    }
    if (contact.phone) {
      htmlContent += `<p>Phone: ${contact.phone}</p>`;
    }
    if (contact.address) {
      htmlContent += `<p>Address: ${contact.address}</p>`;
    }
  }

  return htmlContent;
}

/**
 * Fine-tunes a resume based on a job description
 * This function takes raw resume text and job description,
 * then uses AI to create an optimized resume tailored for the specific job
 * 
 * @param rawText The raw text content of the resume
 * @param jobDescription The job description to optimize for
 * @returns Fine-tuned HTML content and a list of changes made
 */
export async function fineTuneResumeForJob(rawText: string, jobDescription: string): Promise<FineTuneResponse> {
  try {
    // Use AI to fine-tune the resume for the job description
    return await fineTuneResumeWithAI(rawText, jobDescription);
  } catch (error) {
    console.error('Error fine-tuning resume with AI:', error);
    
    // Fallback: convert raw text to HTML and return with error message
    const fallbackHtml = await transformResumeTextToHtml(rawText);
    return {
      htmlContent: fallbackHtml,
      changes: ['Failed to fine-tune resume. Showing basic formatted version.']
    };
  }
}

/**
 * Use LangChain to call an AI service to fine-tune a resume for a specific job
 */
async function fineTuneResumeWithAI(rawText: string, jobDescription: string): Promise<FineTuneResponse> {
  try {
    // Initialize the AI model using the factory
    const model = createAIModelFromEnv();

    // Create a prompt template for resume fine-tuning
    const promptTemplate = PromptTemplate.fromTemplate(
      `You are a professional resume optimizer. Your task is to analyze the provided raw resume text and create an optimized HTML resume tailored to match the job description.
      
      Raw Resume Text:
      {rawText}
      
      Job Description:
      {jobDescription}
      
      Instructions:
      1. Analyze the job description to identify key skills, qualifications, and responsibilities.
      2. Parse and understand the raw resume text content.
      3. Create a well-structured HTML resume that highlights relevant experience and skills matching the job description.
      4. Reword achievements and descriptions to better align with the job requirements.
      5. Emphasize skills and experiences from the raw text that are most relevant to the job.
      6. Use proper HTML structure with appropriate tags (h1, h2, p, ul, li, strong, etc.).
      7. Do not invent new experiences or qualifications that aren't mentioned in the original resume text.
      
      Return a JSON object with the following structure:
      {{
        "htmlContent": "The complete optimized HTML content of the resume",
        "changes": ["List of specific optimizations made to tailor the resume for this job"]
      }}
      
      The changes should be specific and explain what was emphasized or modified and why.`
    );

    // Format the prompt with the raw text and job description
    const formattedPrompt = await promptTemplate.format({
      rawText,
      jobDescription,
    });

    // Call the AI model
    const response = await model.invoke(formattedPrompt);

    // Extract JSON content from the response
    const responseText = response.content.toString();

    // Parse the JSON response
    let jsonResponse: FineTuneResponse;

    try {
      // Try to parse the JSON directly
      jsonResponse = JSON.parse(responseText);
    } catch {
      // If direct parsing fails, try to extract JSON from markdown code blocks
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/) ||
        responseText.match(/```\n([\s\S]*?)\n```/) ||
        responseText.match(/{[\s\S]*?}/);

      if (jsonMatch) {
        try {
          jsonResponse = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } catch {
          throw new Error('Failed to parse AI response as JSON');
        }
      } else {
        throw new Error('Failed to extract JSON from AI response');
      }
    }

    // Validate the response structure
    if (!jsonResponse.htmlContent) {
      throw new Error('AI response missing htmlContent field');
    }

    if (!Array.isArray(jsonResponse.changes)) {
      jsonResponse.changes = ['Resume optimized for the job description'];
    }

    return jsonResponse;
  } catch (error) {
    console.error('Error calling AI service for fine-tuning:', error);
    throw error; // Rethrow to trigger the fallback
  }
}
