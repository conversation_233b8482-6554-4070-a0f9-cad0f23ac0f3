import LoadingSpinner from './LoadingSpinner';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: any;
}

interface HtmlResumePreviewSectionProps {
  parsedResume: ParsedResume;
  editorContent: string | null | undefined;
  fineTuneChanges: string[];
  downloading: boolean;
  downloadError: string | null;
  existingUser: string;
  getHtmlContent: () => string;
  onDownload: (userId: string, htmlContent: string, fileName: string) => Promise<boolean>;
  onClearDownloadError: () => void;
}

export default function HtmlResumePreviewSection({
  parsedResume,
  editorContent,
  fineTuneChanges,
  downloading,
  downloadError,
  existingUser,
  getHtmlContent,
  onDownload,
  onClearDownloadError
}: HtmlResumePreviewSectionProps) {
  const handleDownload = async () => {
    if (existingUser) {
      const fileName = fineTuneChanges.length > 0 
        ? `fine-tuned-resume-${new Date().toISOString().split('T')[0]}.pdf`
        : `resume-${new Date().toISOString().split('T')[0]}.pdf`;
      
      const success = await onDownload(existingUser, getHtmlContent(), fileName);
      if (success) {
        onClearDownloadError();
      }
    }
  };

  // Only show if we have editor content or fine-tune changes
  if (!parsedResume || (!editorContent && fineTuneChanges.length === 0)) {
    return null;
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mt-5">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">HTML Resume Preview</h2>
        <button
          onClick={handleDownload}
          disabled={downloading}
          className={`flex items-center space-x-2 py-2 px-4 rounded text-white font-medium transition-colors ${
            downloading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600'
          }`}
        >
          {downloading ? (
            <>
              <LoadingSpinner size="small" />
              <span>Downloading...</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Download PDF</span>
            </>
          )}
        </button>
      </div>

      {/* Download Error Display */}
      {downloadError && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded flex justify-between items-center">
          <span>{downloadError}</span>
          <button
            onClick={onClearDownloadError}
            className="text-red-500 hover:text-red-700 ml-2"
          >
            ×
          </button>
        </div>
      )}

      {fineTuneChanges.length > 0 && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-medium text-green-800 mb-2">
            Resume Fine-tuned for Job Description
          </h3>
          <p className="text-sm text-green-700 mb-3">
            The following changes were made to optimize your resume:
          </p>
          <ul className="list-disc pl-5 space-y-1 text-sm text-green-800">
            {fineTuneChanges.map((change, index) => (
              <li key={index}>{change}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Improved iframe with error handling */}
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <iframe
          srcDoc={getHtmlContent()}
          className="w-full h-96"
          title="Resume Preview"
          sandbox="allow-same-origin"
          onLoad={() => {
            console.log('Iframe loaded successfully');
          }}
          onError={(e) => {
            console.error('Iframe error:', e);
          }}
        />
      </div>

      {/* Fallback: Show raw HTML if needed */}
      {/* <details className="mt-4">
        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
          View Raw HTML Content
        </summary>
        <pre className="mt-2 p-3 bg-gray-100 border rounded text-xs overflow-auto max-h-40">
          {getHtmlContent()}
        </pre>
      </details> */}
    </div>
  );
}
