#!/bin/bash

# Make the script executable
chmod +x docker-start.sh

# Function to display help message
show_help() {
  echo "Usage: ./docker-start.sh [OPTION]"
  echo "Start the PayDai application with Docker Compose."
  echo ""
  echo "Options:"
  echo "  dev       Start in development mode with hot reloading"
  echo "  prod      Start in production mode"
  echo "  down      Stop and remove containers"
  echo "  seed      Seed the database with sample data"
  echo "  help      Display this help message"
  echo ""
  echo "Examples:"
  echo "  ./docker-start.sh dev    # Start in development mode"
  echo "  ./docker-start.sh down   # Stop containers"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
  echo "Error: Docker is not installed. Please install Docker first."
  exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose first."
  exit 1
fi

# Process command line arguments
case "$1" in
  dev)
    echo "Starting PayDai in development mode..."
    docker-compose -f docker-compose.dev.yml up --force-recreate
    ;;
  prod)
    echo "Starting PayDai in production mode..."
    docker-compose up
    ;;
  down)
    echo "Stopping PayDai containers..."
    docker-compose down
    ;;
  seed)
    echo "Seeding the database..."
    docker-compose exec nextjs-app npm run seed
    ;;
  help)
    show_help
    ;;
  *)
    echo "Invalid option: $1"
    show_help
    exit 1
    ;;
esac
