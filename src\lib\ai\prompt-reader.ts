import fs from 'fs';
import path from 'path';
import { PromptTemplate } from "@langchain/core/prompts";

/**
 * TypeScript interfaces for the structured resume parsing response
 */
export interface ProfileUrl {
  type: string;
  url: string;
}

export interface PersonalDetails {
  name: string;
  phone_numbers: string[];
  email_addresses: string[];
  profile_urls: ProfileUrl[];
  location: string;
}

export interface SummaryObjective {
  summary_text: string;
}

export interface SkillCategory {
  category: string;
  items: string[];
}

export interface WorkExperience {
  company_name: string;
  job_title: string;
  location: string;
  start_date: string;
  end_date: string;
  duration: string;
  responsibilities_achievements: string[];
}

export interface Education {
  degree: string;
  major: string;
  institution_name: string;
  location: string;
  graduation_date: string;
  gpa_score: string;
}

export interface Certification {
  certification_name: string;
  issuing_body: string;
  issue_date: string;
  expiration_date: string;
}

export interface OtherSections {
  awards_honors: string[];
  projects: string[];
  publications: string[];
  volunteer_experience: string[];
  interests: string[];
  references_available: boolean;
}

export interface ParsedResumeResponse {
  personal_details: PersonalDetails;
  summary_objective: SummaryObjective;
  skills: SkillCategory[];
  work_experience: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  other_sections: OtherSections;
}

/**
 * Reads and processes a markdown prompt file for use with LangChain
 * 
 * @param promptPath Path to the markdown prompt file
 * @returns PromptTemplate ready for use with LangChain
 */
export function readPromptFromMarkdown(promptPath: string): PromptTemplate {
  try {
    // Read the markdown file
    const fullPath = path.resolve(process.cwd(), promptPath);
    const markdownContent = fs.readFileSync(fullPath, 'utf-8');
    
    // Convert markdown to a LangChain-compatible prompt
    const promptTemplate = convertMarkdownToPrompt(markdownContent);
    
    return PromptTemplate.fromTemplate(promptTemplate);
  } catch (error) {
    console.error('Error reading prompt from markdown:', error);
    throw new Error(`Failed to read prompt from ${promptPath}: ${error}`);
  }
}

/**
 * Converts markdown prompt content to a LangChain-compatible template string
 * 
 * @param markdownContent The raw markdown content
 * @returns Formatted prompt template string
 */
function convertMarkdownToPrompt(markdownContent: string): string {
  // Remove markdown formatting and convert to plain text prompt
  let prompt = markdownContent;
  
  // Remove markdown headers (### -> "")
  prompt = prompt.replace(/^#{1,6}\s*/gm, '');
  
  // Remove markdown emphasis (* and **)
  prompt = prompt.replace(/\*\*(.*?)\*\*/g, '$1');
  prompt = prompt.replace(/\*(.*?)\*/g, '$1');
  
  // Remove markdown code blocks
  prompt = prompt.replace(/```[\s\S]*?```/g, '');
  
  // Clean up extra whitespace and line breaks
  prompt = prompt.replace(/\n{3,}/g, '\n\n');
  prompt = prompt.trim();
  
  // Add the template variable for resume text
  prompt += '\n\n**RESUME TEXT TO PARSE:**\n{resumeText}\n\n';
  
  // Add instruction for JSON output
  prompt += `**OUTPUT INSTRUCTIONS:**
Return only a valid JSON object with the following structure. Do not include any explanations or additional text:

{{
  "personal_details": {{
    "name": "string or null",
    "phone_numbers": ["string"],
    "email_addresses": ["string"],
    "profile_urls": [
      {{
        "type": "string",
        "url": "string"
      }}
    ],
    "location": "string or null"
  }},
  "summary_objective": {{
    "summary_text": "string or null"
  }},
  "skills": [
    {{
      "category": "string",
      "items": ["string"]
    }}
  ],
  "work_experience": [
    {{
      "company_name": "string or null",
      "job_title": "string or null",
      "location": "string or null",
      "start_date": "string or null",
      "end_date": "string or null",
      "duration": "string or null",
      "responsibilities_achievements": ["string"]
    }}
  ],
  "education": [
    {{
      "degree": "string or null",
      "major": "string or null",
      "institution_name": "string or null",
      "location": "string or null",
      "graduation_date": "string or null",
      "gpa_score": "string or null"
    }}
  ],
  "certifications": [
    {{
      "certification_name": "string or null",
      "issuing_body": "string or null",
      "issue_date": "string or null",
      "expiration_date": "string or null"
    }}
  ],
  "other_sections": {{
    "awards_honors": ["string"],
    "projects": ["string"],
    "publications": ["string"],
    "volunteer_experience": ["string"],
    "interests": ["string"],
    "references_available": false
  }}
}}`;
  
  return prompt;
}

/**
 * Get the resume parsing prompt template
 * 
 * @returns PromptTemplate for resume parsing
 */
export function getResumeParsingPrompt(): PromptTemplate {
  return readPromptFromMarkdown('src/prompts/parse-resume.md');
}

/**
 * Get the resume-job matching prompt template
 * 
 * @returns PromptTemplate for resume-job matching
 */
export function getResumeJobMatchPrompt(): PromptTemplate {
  return readPromptFromMarkdown('src/prompts/resume-job-match.md');
}
