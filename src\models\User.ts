import mongoose, { Schema, Document, Model } from 'mongoose';
import { hashPassword } from '@/lib/auth/password';

// Define the User interface
export interface IUser extends Document {
  name: string;
  email: string;
  password?: string; // Optional for social login users
  googleId?: string; // Google OAuth ID
  avatar?: string; // Profile picture URL
  provider: 'email' | 'google'; // Authentication provider
  roles: ('admin' | 'user')[];
  fineTuneCredits: {
    remainingCredits: number;
    totalUsed: number;
    lastPurchasedCredits: number;
    lastCreditPurchasedAt?: Date;
    firstUsedAt?: Date;
    lastUsedAt?: Date;
  };
  paymentStatus: {
    paidAt?: Date;
    paymentReference?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Define the User schema
const UserSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a name'],
      maxlength: [60, 'Name cannot be more than 60 characters'],
    },
    email: {
      type: String,
      required: [true, 'Please provide an email'],
      unique: true,
      lowercase: true,
      trim: true,
      validate: {
        validator: function (v: string) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
        },
        message: 'Please enter a valid email',
      },
    },
    password: {
      type: String,
      required: function(this: IUser) {
        return this.provider === 'email';
      },
      minlength: [8, 'Password must be at least 8 characters'],
    },
    googleId: {
      type: String,
      sparse: true, // Allows multiple null values but unique non-null values
    },
    avatar: {
      type: String,
    },
    provider: {
      type: String,
      enum: ['email', 'google'],
      default: 'email',
      required: true,
    },
    roles: {
      type: [String],
      enum: ['admin', 'user'],
      default: ['user'],
      required: true,
    },
    fineTuneCredits: {
      remainingCredits: {
        type: Number,
        default: 5, // Users start with 5 free credits
      },
      totalUsed: {
        type: Number,
        default: 0,
      },
      lastPurchasedCredits: {
        type: Number,
        default: 0,
      },
      lastCreditPurchasedAt: {
        type: Date,
      },
      firstUsedAt: {
        type: Date,
      },
      lastUsedAt: {
        type: Date,
      },
    },
    paymentStatus: {
      paidAt: {
        type: Date,
      },
      paymentReference: {
        type: String,
      },
    },
  },
  {
    timestamps: true,
  }
);

// Add a pre-save hook to hash the password before saving
UserSchema.pre('save', async function (next) {
  // Only hash the password if it has been modified (or is new) and exists
  if (!this.isModified('password') || !this.password) return next();

  try {
    // Hash the password with type assertion for 'this'
    const user = this as unknown as IUser;
    if (user.password) {
      const hashedPassword = await hashPassword(user.password);
      user.password = hashedPassword;
    }
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Create and export the User model
export default (mongoose.models.User as Model<IUser>) ||
  mongoose.model<IUser>('User', UserSchema);
