import { useState, useRef } from 'react';
import { authenticatedFetch } from '@/lib/auth/token-storage';
import { ParsedResumeResponse } from '@/lib/ai/prompt-reader';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: ParsedResumeResponse;
}

export const useResumeUpload = () => {
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [parsedResume, setParsedResume] = useState<ParsedResume | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle resume file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setResumeFile(e.target.files[0]);
    }
  };

  // Handle resume upload
  const handleResumeUpload = async (
    existingUser: string | null,
    setExistingUser: (userId: string) => void,
    setError: (error: string) => void
  ) => {
    if (!resumeFile) {
      setError('Please select a PDF resume file');
      return false;
    }

    if (resumeFile.type !== 'application/pdf') {
      setError('Only PDF files are accepted');
      return false;
    }

    try {
      setUploading(true);
      setError('');
      setParsedResume(null);

      let userId = existingUser;

      // If no existing user, create a temporary user for the resume upload
      if (!userId) {
        const userResponse = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Resume User',
            email: `resume_${Date.now()}@example.com`,
            password: 'password123',
          }),
        });

        const userResult = await userResponse.json();

        if (!userResult.success) {
          throw new Error(userResult.error || 'Failed to create temporary user');
        }

        userId = userResult.data._id;
        if (userId) {
          setExistingUser(userId);
        }
      }

      // Upload the resume for the user
      if (!userId) {
        throw new Error('User ID is required for resume upload');
      }

      const formData = new FormData();
      formData.append('file', resumeFile);

      const resumeResponse = await authenticatedFetch(`/api/users/${userId}/resume`, {
        method: 'POST',
        body: formData,
      });

      const resumeResult = await resumeResponse.json();

      if (resumeResult.success) {
        setParsedResume(resumeResult.data);
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        setResumeFile(null);
        return true;
      } else {
        throw new Error(resumeResult.error?.message || 'Failed to upload resume');
      }
    } catch (err) {
      setError(`An error occurred: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error(err);
      return false;
    } finally {
      setUploading(false);
    }
  };

  // Reset the form to upload another resume
  const handleReset = () => {
    setParsedResume(null);
    setResumeFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Generate HTML content from parsed resume data
  const generateHtmlContent = (editorContent?: string): string => {
    // If we have custom editor content (from fine-tuning), use it
    if (editorContent) {
      return editorContent;
    }

    if (!parsedResume) return '<p>No resume data available.</p>';

    // If we have AI-generated HTML content, use it
    if (parsedResume.htmlContent) {
      return parsedResume.htmlContent;
    }

    // Fallback to generating HTML from AI parsed resume data
    if (parsedResume.aiParsedResume) {
      let content = '<h1>Resume</h1>';

      // Add personal details
      if (parsedResume.aiParsedResume.personal_details.name) {
        content += `<h2>${parsedResume.aiParsedResume.personal_details.name}</h2>`;
      }

      // Add contact information
      const personalDetails = parsedResume.aiParsedResume.personal_details;
      if (personalDetails.email_addresses.length > 0 || personalDetails.phone_numbers.length > 0 || personalDetails.location) {
        content += '<h3>Contact Information</h3>';
        personalDetails.email_addresses.forEach(email => {
          content += `<p>Email: ${email}</p>`;
        });
        personalDetails.phone_numbers.forEach(phone => {
          content += `<p>Phone: ${phone}</p>`;
        });
        if (personalDetails.location) {
          content += `<p>Location: ${personalDetails.location}</p>`;
        }
      }

      // Add summary
      if (parsedResume.aiParsedResume.summary_objective.summary_text) {
        content += '<h3>Professional Summary</h3>';
        content += `<p>${parsedResume.aiParsedResume.summary_objective.summary_text}</p>`;
      }

      // Add skills section
      if (parsedResume.aiParsedResume.skills.length > 0) {
        content += '<h3>Skills</h3>';
        parsedResume.aiParsedResume.skills.forEach(skillCategory => {
          content += `<h4>${skillCategory.category}</h4><ul>`;
          skillCategory.items.forEach(skill => {
            content += `<li>${skill}</li>`;
          });
          content += '</ul>';
        });
      }

      // Add work experience
      if (parsedResume.aiParsedResume.work_experience.length > 0) {
        content += '<h3>Work Experience</h3>';
        parsedResume.aiParsedResume.work_experience.forEach(work => {
          content += `<div><h4>${work.job_title || 'Position'} at ${work.company_name || 'Company'}</h4>`;
          content += `<p>${work.start_date} - ${work.end_date}</p>`;
          if (work.responsibilities_achievements.length > 0) {
            content += '<ul>';
            work.responsibilities_achievements.forEach(item => {
              content += `<li>${item}</li>`;
            });
            content += '</ul>';
          }
          content += '</div>';
        });
      }

      // Add education
      if (parsedResume.aiParsedResume.education.length > 0) {
        content += '<h3>Education</h3>';
        parsedResume.aiParsedResume.education.forEach(edu => {
          content += `<div><h4>${edu.degree || 'Degree'}</h4>`;
          content += `<p>${edu.institution_name || 'Institution'}</p>`;
          if (edu.graduation_date) {
            content += `<p>Graduated: ${edu.graduation_date}</p>`;
          }
          content += '</div>';
        });
      }

      return content;
    }

    return '<p>No resume content available for preview.</p>';
  };

  return {
    resumeFile,
    uploading,
    parsedResume,
    fileInputRef,
    handleFileChange,
    handleResumeUpload,
    handleReset,
    generateHtmlContent,
    setParsedResume,
  };
};
