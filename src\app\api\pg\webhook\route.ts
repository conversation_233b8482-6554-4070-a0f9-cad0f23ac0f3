import { NextRequest, NextResponse } from 'next/server';
import { Cashfree, CFEnvironment } from 'cashfree-pg';
import connectToDatabase from '@/lib/mongodb';
import Transaction from '@/models/Transaction';
import User from '@/models/User';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

const { CASHFREE_CLIENT_ID, CASHFREE_CLIENT_SECRET, CASHFREE_ENV } = 
  process.env as Record<string, string | undefined>;

const cfEnv = 
  (CASHFREE_ENV || 'sandbox').toLowerCase() === 'production'
    ? CFEnvironment.PRODUCTION 
    : CFEnvironment.SANDBOX;

export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    if (!CASHFREE_CLIENT_ID || !CASHFREE_CLIENT_SECRET) {
      console.error('Cashfree credentials not configured');
      return NextResponse.json(
        { error: 'Cashfree credentials not configured' },
        { status: 500 }
      );
    }

    // Initialize Cashfree SDK
    const cashfree = new Cashfree(
      cfEnv,
      CASHFREE_CLIENT_ID,
      CASHFREE_CLIENT_SECRET
    );

    // Get webhook headers
    const signature = req.headers.get('x-webhook-signature');
    const timestamp = req.headers.get('x-webhook-timestamp');

    if (!signature || !timestamp) {
      console.error('Missing webhook signature or timestamp');
      return NextResponse.json(
        { error: 'Missing webhook signature or timestamp' },
        { status: 400 }
      );
    }

    // Get raw body for signature verification
    const rawBody = await req.text();

    try {
      // Verify webhook signature
      cashfree.PGVerifyWebhookSignature(signature, rawBody, timestamp);
      console.log('Webhook signature verified successfully');
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Webhook signature verification failed' },
        { status: 401 }
      );
    }

    // Parse the webhook payload
    const webhookData = JSON.parse(rawBody);
    console.log('Webhook received:', webhookData);

    // Extract order details from webhook
    const { type, data } = webhookData;
    
    if (type === 'PAYMENT_SUCCESS_WEBHOOK') {
      const orderData = data?.order || data;
      const orderId = orderData?.order_id;
      const orderStatus = orderData?.order_status;

      if (!orderId) {
        console.error('Order ID missing in webhook data');
        return NextResponse.json(
          { error: 'Order ID missing' },
          { status: 400 }
        );
      }

      // Update transaction status if payment is successful
      if (orderStatus === 'PAID') {
        try {
          // Find and update the transaction
          const updatedTransaction = await Transaction.findOneAndUpdate(
            { orderId: orderId },
            {
              status: 'PAID',
              $set: {
                'paymentDetails.cfPaymentId': orderData.cf_order_id,
                'paymentDetails.paymentTime': new Date(orderData.created_at),
                'paymentDetails.webhookReceived': new Date(),
              }
            },
            { new: true }
          );

          if (updatedTransaction) {
            console.log('Transaction updated successfully:', updatedTransaction._id);

            // Calculate credits and update user
            const transactionAmount = updatedTransaction.amount / 100; // Convert from paise to rupees
            const creditsToAdd = Math.max(1, Math.floor(transactionAmount / 10));

            const updatedUser = await User.findByIdAndUpdate(
              updatedTransaction.userId,
              {
                $inc: {
                  'fineTuneCredits.remainingCredits': creditsToAdd,
                },
                $set: {
                  'fineTuneCredits.lastPurchasedCredits': creditsToAdd,
                  'fineTuneCredits.lastCreditPurchasedAt': new Date(orderData.created_at),
                  'paymentStatus.paidAt': new Date(orderData.created_at),
                  'paymentStatus.paymentReference': updatedTransaction._id,
                }
              },
              { new: true }
            );

            if (updatedUser) {
              console.log(`User ${updatedUser._id} credits updated: +${creditsToAdd} credits`);
            }
          } else {
            console.warn('Transaction not found for order ID:', orderId);
          }
        } catch (dbError) {
          console.error('Database update error:', dbError);
          // Don't return error to Cashfree - webhook was valid, just DB issue
        }
      }
    }

    // Respond with success to acknowledge webhook receipt
    return NextResponse.json(
      { 
        success: true,
        message: 'Webhook processed successfully' 
      },
      { status: 200 }
    );

  } catch (error: any) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle GET requests (for webhook URL verification if needed)
export async function GET(req: NextRequest) {
  return NextResponse.json(
    { 
      message: 'Cashfree webhook endpoint is active',
      timestamp: new Date().toISOString()
    },
    { status: 200 }
  );
}