import mongoose, { Schema, Document, Model } from 'mongoose';
import { IChatMessage } from '@/types/chat';

// Define the ChatMessage schema
const ChatMessageSchema: Schema = new Schema(
  {
    sessionId: {
      type: Schema.Types.ObjectId,
      ref: 'ChatSession',
      required: [true, 'Session ID is required'],
    },
    role: {
      type: String,
      required: [true, 'Role is required'],
      enum: ['system', 'assistant', 'user'],
    },
    content: {
      type: String,
      required: [true, 'Content is required'],
    },
    messageType: {
      type: String,
      required: [true, 'Message type is required'],
      default: 'general',
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Add indexes for better query performance
ChatMessageSchema.index({ sessionId: 1, createdAt: 1 });
ChatMessageSchema.index({ role: 1 });
ChatMessageSchema.index({ messageType: 1 });

// Create and export the ChatMessage model
export default (mongoose.models.ChatMessage as Model<IChatMessage & Document>) || 
  mongoose.model<IChatMessage & Document>('ChatMessage', ChatMessageSchema);
