'use client';

import React from 'react';
import PdfPreview from '@/components/PdfPreview';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: any;
}

interface ResumePreviewSectionProps {
  parsedResume: ParsedResume;
  existingUser: string;
  showEditor: boolean;
  onReset: () => void;
  onToggleEditor: () => void;
}

export default function ResumePreviewSection({
  parsedResume,
  existingUser,
  showEditor,
  onReset,
  onToggleEditor,
}: ResumePreviewSectionProps) {
  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Resume Preview</h2>
        <button
          onClick={onReset}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded transition-colors"
        >
          Upload Another Resume
        </button>
      </div>

      <PdfPreview
        userId={existingUser}
        fileName={parsedResume.fileName}
        fileSize={parsedResume.fileSize}
        uploadDate={parsedResume.uploadDate}
        onViewDetails={onToggleEditor}
      />
    </div>
  );
}
