'use client';

import React from 'react';
import { StarIcon } from 'lucide-react';
import Image from 'next/image';

export const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON><PERSON>',
      position: 'Marketing Specialist',
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      quote: 'After using PaydAI for just one month, I received interview invitations from 3 companies I was really excited about. The resume customization tool made all the difference!',
      rating: 5
    },
    {
      name: '<PERSON><PERSON>',
      position: 'Software Developer',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      quote: "PaydAI helped me identify which jobs were the best match for my skills, saving me hours of time applying to positions that weren't right for me. I landed my dream job in just 3 weeks!",
      rating: 5
    },
    {
      name: 'Divya Deshmukh',
      position: 'Project Manager',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      quote: "The cover letter generator is incredible! It helped me create personalized letters that really showcased my experience. I've recommended PaydAI to all my friends looking for new opportunities.",
      rating: 4
    }
  ];

  return (
    <section id="testimonials" style={{ background: 'white', color: 'black' }} className="py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our Users Say
          </h2>
          <p className="text-xl max-w-3xl mx-auto">
            Thousands of job seekers have boosted their career opportunities
            with PaydAI.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="rounded-lg p-6 border" style={{ background: 'white', color: 'black', borderColor: 'black' }}>
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <StarIcon 
                    key={i} 
                    className="h-5 w-5" 
                    style={{ color: i < testimonial.rating ? 'black' : '#ccc' }} 
                    fill={i < testimonial.rating ? 'currentColor' : 'none'} 
                  />
                ))}
              </div>
              <p className="italic mb-6">&ldquo;{testimonial.quote}&rdquo;</p>
              <div className="flex items-center">
                <Image 
                  src={testimonial.image} 
                  alt={testimonial.name} 
                  className="h-12 w-12 rounded-full object-cover mr-4" 
                  width={48}
                  height={48}
                />
                <div>
                  <h4 className="font-semibold">
                    {testimonial.name}
                  </h4>
                  <p className="text-sm">
                    {testimonial.position}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
