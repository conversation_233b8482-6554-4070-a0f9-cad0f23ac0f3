import { handleApiError } from '@/lib/api/error-handler';
import { validateEditorJSData } from '@/lib/editor/resume-transformer';
import connectToDatabase from '@/lib/mongodb';
import { parseResumePdf } from '@/lib/pdf/pdf-parser';
import { parseResumeText } from '@/lib/ai/resume-parser-service';
import { ParsedResumeResponse } from '@/lib/ai/prompt-reader';
import { parseFileUpload, validatePdfFile } from '@/lib/uploads/file-handler';
import { withAuthAndOwnership } from '@/lib/auth/middleware';
import User from '@/models/User';
import UserProfile from '@/models/UserProfile';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';
import pdfParse from 'pdf-parse';

/**
 * POST handler for uploading and processing a user's resume
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response with the processed resume data
 */
export const POST = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Connect to the database
      await connectToDatabase();

      // Check if user exists
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Parse the uploaded file
      const uploadedFile = await parseFileUpload(req);

      // Validate the file is a PDF
      validatePdfFile(uploadedFile);

      // Extract raw text from PDF
      const pdfData = await pdfParse(uploadedFile.buffer);
      const rawText = pdfData.text;
      
      // Use AI service to parse the resume text
      const aiParsedResume = await parseResumeText(rawText);
      
      // Create new resume object
      const newResume = {
        rawText: rawText,
        fileName: uploadedFile.filename,
        fileSize: uploadedFile.size,
        mimeType: uploadedFile.mimetype,
        uploadDate: new Date(),
        pdfBuffer: uploadedFile.buffer,
        aiParsedResume: aiParsedResume,
      };

      // Find or create user profile and add the new resume
      let userProfile = await UserProfile.findOne({ user: userId });
      
      if (!userProfile) {
        // Create new profile with the first resume
        userProfile = new UserProfile({
          user: userId,
          resumes: [newResume],
        });
      } else {
        // Add new resume to existing profile
        userProfile.resumes.push(newResume);
      }
      
      await userProfile.save();

      // Get the latest resume (last one added)
      const latestResume = userProfile.resumes[userProfile.resumes.length - 1];

      // Return success response with the processed data
      return NextResponse.json(
        {
          success: true,
          data: {
            userId,
            fileName: latestResume.fileName,
            fileSize: latestResume.fileSize,
            uploadDate: latestResume.uploadDate,
            aiParsedResume: latestResume.aiParsedResume,
          },
        },
        { status: 201 }
      );
    } catch (error) {
      return handleApiError(error);
    }
  },
  (params) => params.id
);

/**
 * GET handler for retrieving a user's resume data
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response with the user's resume data
 */
export const GET = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Connect to the database
      await connectToDatabase();

      // Check if user exists
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Find the user profile
      const userProfile = await UserProfile.findOne({ user: userId });
      if (!userProfile || !userProfile.resumes || userProfile.resumes.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Resume not found for this user',
              code: 'resume_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Get the latest resume (last one added)
      const latestResume = userProfile.resumes[userProfile.resumes.length - 1];

      // Return the resume data
      return NextResponse.json({
        success: true,
        data: {
          userId,
          fileName: latestResume.fileName,
          fileSize: latestResume.fileSize,
          uploadDate: latestResume.uploadDate,
          resumeStructuredData: latestResume.resumeStructuredData,
          aiParsedResume: latestResume.aiParsedResume,
          htmlContent: latestResume.htmlContent || '',
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  },
  (params) => params.id
);

/**
 * DELETE handler for removing a user's resume
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response indicating success or failure
 */
/**
 * PUT handler for updating a user's resume data (especially EditorJS data)
 * @param req The request object
 * @param params The route parameters, including the user ID
 * @returns A response indicating success or failure
 */
export const PUT = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Connect to the database
      await connectToDatabase();

      // Check if user exists
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Get request body
      const body = await req.json();

      // Check if resume data is provided
      if (!body.resumeStructuredData) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Resume data is required',
              code: 'missing_data',
            },
          },
          { status: 400 }
        );
      }

      // Validate resume data
      if (!validateEditorJSData(body.resumeStructuredData)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid resume data format',
              code: 'invalid_data',
            },
          },
          { status: 400 }
        );
      }

      // Find the user profile
      const userProfile = await UserProfile.findOne({ user: userId });
      if (!userProfile || !userProfile.resumes || userProfile.resumes.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Resume not found for this user',
              code: 'resume_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Update the structured resume data for the latest resume
      const latestResumeIndex = userProfile.resumes.length - 1;
      userProfile.resumes[latestResumeIndex].resumeStructuredData = body.resumeStructuredData;
      await userProfile.save();

      // Return success response
      return NextResponse.json({
        success: true,
        message: 'Resume data updated successfully',
        data: {
          userId,
          resumeStructuredData: userProfile.resumes[latestResumeIndex].resumeStructuredData,
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  },
  (params) => params.id
);

export const DELETE = withAuthAndOwnership(
  async (req: NextRequest, context, _authenticatedUser) => {
    try {
      // Validate user ID format
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid parameters',
              code: 'invalid_params',
            },
          },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Invalid user ID format',
              code: 'invalid_id',
            },
          },
          { status: 400 }
        );
      }

      // Connect to the database
      await connectToDatabase();

      // Check if user exists
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'User not found',
              code: 'user_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Delete the user profile
      const result = await UserProfile.findOneAndDelete({ user: userId });
      if (!result) {
        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Resume not found for this user',
              code: 'resume_not_found',
            },
          },
          { status: 404 }
        );
      }

      // Return success response
      return NextResponse.json({
        success: true,
        message: 'Resume deleted successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  },
  (params) => params.id
);
