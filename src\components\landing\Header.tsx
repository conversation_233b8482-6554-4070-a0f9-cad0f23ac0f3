'use client';

import React, { useState } from 'react';
import { MenuIcon, XIcon } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { ProfileIcon } from '@/components/ProfileIcon';

export const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { data: session } = useSession();

  return (
    <header style={{ background: 'white', color: 'black' }} className="w-full border-b sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image src="/logos/logo1.png" alt="PaydAI" width={120} height={40} className="h-8 w-auto" />
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#how-it-works" className="hover:underline transition-colors" style={{ color: 'black' }}>
            How It Works
          </a>
          <a href="#testimonials" className="hover:underline transition-colors" style={{ color: 'black' }}>
            Testimonials
          </a>
          <a href="#pricing" className="hover:underline transition-colors" style={{ color: 'black' }}>
            Pricing
          </a>
          <Link href="/users" className="hover:underline transition-colors" style={{ color: 'black' }}>
            User Management
          </Link>
          <Link href="/app" className="px-5 py-2 rounded font-medium hover:underline transition-colors" style={{ background: 'black', color: 'white' }}>
            Get Started
          </Link>
          {session ? (
            <ProfileIcon />
          ) : (
            <Link href="/login" className="border px-5 py-2 rounded hover:underline transition-colors" style={{ color: 'black', borderColor: 'black' }}>
              Log In
            </Link>
          )}
        </nav>
        
        {/* Mobile menu button */}
        <button 
          className="md:hidden focus:outline-none" 
          style={{ color: 'black' }}
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
        </button>
      </div>
      
      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t" style={{ background: 'white', color: 'black' }}>
          <div className="container mx-auto px-4 py-3 flex flex-col space-y-3">
            <a href="#how-it-works" className="py-2 hover:underline transition-colors" style={{ color: 'black' }}>
              How It Works
            </a>
            <a href="#testimonials" className="py-2 hover:underline transition-colors" style={{ color: 'black' }}>
              Testimonials
            </a>
            <a href="#pricing" className="py-2 hover:underline transition-colors" style={{ color: 'black' }}>
              Pricing
            </a>
            <Link href="/users" className="py-2 hover:underline transition-colors" style={{ color: 'black' }}>
              User Management
            </Link>
            <Link href="/app" className="px-5 py-2 rounded font-medium text-center hover:underline transition-colors" style={{ background: 'black', color: 'white' }}>
              Get Started
            </Link>
            {session ? (
              <div className="flex justify-center py-2">
                <ProfileIcon />
              </div>
            ) : (
              <Link href="/login" className="border px-5 py-2 rounded text-center hover:underline transition-colors" style={{ color: 'black', borderColor: 'black' }}>
                Log In
              </Link>
            )}
          </div>
        </div>
      )}
    </header>
  );
};
