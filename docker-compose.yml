version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: mongodb
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=paydai
    networks:
      - app-network

  # Next.js application service
  nextjs-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nextjs-app
    restart: always
    ports:
      - "3050:3050"
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/paydai
      - NODE_ENV=production
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge
