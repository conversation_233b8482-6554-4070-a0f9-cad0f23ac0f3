import React from 'react';
import LoadingSpinner from './LoadingSpinner';

interface CreditDisplayProps {
  remainingCredits: number;
  totalUsed: number;
  isFirstTime: boolean;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

export default function CreditDisplay({
  remainingCredits,
  totalUsed,
  isFirstTime,
  loading = false,
  error = null,
  className = '',
}: CreditDisplayProps) {
  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <LoadingSpinner size="small" />
        <span className="text-sm text-gray-600">Loading credits...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span className="text-sm">Failed to load credits</span>
      </div>
    );
  }

  const getCreditColor = () => {
    if (remainingCredits === 0) return 'text-red-600';
    if (remainingCredits <= 2) return 'text-orange-600';
    return 'text-green-600';
  };

  const getCreditBgColor = () => {
    if (remainingCredits === 0) return 'bg-red-50 border-red-200';
    if (remainingCredits <= 2) return 'bg-orange-50 border-orange-200';
    return 'bg-green-50 border-green-200';
  };

  return (
    <div className={`inline-flex items-center space-x-3 px-4 py-2 rounded-lg border ${getCreditBgColor()} ${className}`}>
      {/* Credit Icon */}
      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${remainingCredits > 0 ? 'bg-blue-100' : 'bg-gray-100'}`}>
        <svg className={`w-4 h-4 ${remainingCredits > 0 ? 'text-blue-600' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      </div>

      {/* Credit Information */}
      <div className="flex flex-col">
        <div className="flex items-center space-x-2">
          <span className={`font-semibold text-lg ${getCreditColor()}`}>
            {remainingCredits}
          </span>
          <span className="text-sm text-gray-600">
            {remainingCredits === 1 ? 'credit' : 'credits'} left
          </span>
        </div>
        
        {/* Additional info */}
        <div className="text-xs text-gray-500">
          {isFirstTime ? (
            <span>Welcome! You have free credits to get started</span>
          ) : (
            <span>{totalUsed} used so far</span>
          )}
        </div>
      </div>

      {/* Warning for low credits */}
      {remainingCredits <= 2 && remainingCredits > 0 && (
        <div className="flex items-center">
          <svg className="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      )}

      {/* No credits warning */}
      {remainingCredits === 0 && (
        <div className="flex items-center">
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
          </svg>
        </div>
      )}
    </div>
  );
}
