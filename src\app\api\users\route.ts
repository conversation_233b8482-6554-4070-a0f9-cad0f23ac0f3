import { handleApiError } from '@/lib/api/error-handler';
import connectToDatabase from '@/lib/mongodb';
import { validateUserInput } from '@/lib/validation';
import { withAuth, withNextAuth } from '@/lib/auth/middleware';
import User from '@/models/User';
import { NextRequest, NextResponse } from 'next/server';

// Admin-only route - requires NextAuth authentication and admin role
export const GET = withNextAuth(async (request, context, authenticatedUser) => {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get the current user to check their role
    const currentUser = await User.findById(authenticatedUser.userId);

    if (!currentUser) {
      return NextResponse.json(
        {
          success: false,
          error: { message: 'User not found.' }
        },
        { status: 404 }
      );
    }

    // Check if authenticated user has admin role
    if (!currentUser.roles.includes('admin')) {
      return NextResponse.json(
        {
          success: false,
          error: { message: 'Access denied. Admin role required to view all users.' }
        },
        { status: 403 }
      );
    }

    // Get all users
    const users = await User.find({}).select('-password');

    // Return the users
    return NextResponse.json({
      success: true,
      data: users,
      authenticatedUser: authenticatedUser.email // Include authenticated user info
    });
  } catch (error) {
    return handleApiError(error);
  }
});

// Admin-only route - requires NextAuth authentication and admin role
export const POST = withNextAuth(async (request, context, authenticatedUser) => {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get the current user to check their role
    const currentUser = await User.findById(authenticatedUser.userId);
    
    if (!currentUser) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'User not found.' } 
        },
        { status: 404 }
      );
    }

    // Check if authenticated user has admin role
    if (!currentUser.roles.includes('admin')) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'Access denied. Admin role required to create users.' } 
        },
        { status: 403 }
      );
    }
    
    // Get the request body
    const body = await request.json();
    
    // Validate user input
    const validation = validateUserInput(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Validation failed', 
            details: validation.errors 
          } 
        },
        { status: 400 }
      );
    }
    
    // Create a new user with explicit credit initialization and default role
    const userData = {
      ...body,
      roles: body.roles || ['user'], // Default to 'user' role if not specified
      fineTuneCredits: {
        remainingCredits: 1,
        totalUsed: 0,
        lastPurchasedCredits: 0
      },
      paymentStatus: {}
    };
    
    const user = await User.create(userData);
    
    // Return the user without the password
    const userWithoutPassword = user.toObject();
    const { password, ...userResponse } = userWithoutPassword;
    
    return NextResponse.json(
      { 
        success: true, 
        data: {
          ...userResponse,
          createdBy: authenticatedUser.email
        }
      },
      { status: 201 }
    );
  } catch (error) {
    return handleApiError(error);
  }
});

// Admin-only route - requires NextAuth authentication and admin role
export const PUT = withNextAuth(async (request, context, authenticatedUser) => {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get the current user to check their role
    const currentUser = await User.findById(authenticatedUser.userId);
    
    if (!currentUser) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'User not found.' } 
        },
        { status: 404 }
      );
    }

    // Check if authenticated user has admin role
    if (!currentUser.roles.includes('admin')) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'Access denied. Admin role required to manage user credits.' } 
        },
        { status: 403 }
      );
    }
    
    // Get the request body
    const body = await request.json();
    const { userId, credits } = body;
    
    // Validate required fields
    if (!userId) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'User ID is required.' } 
        },
        { status: 400 }
      );
    }
    
    if (typeof credits !== 'number' || credits < 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'Credits must be a non-negative number.' } 
        },
        { status: 400 }
      );
    }
    
    // Find the user to update
    const userToUpdate = await User.findById(userId);
    
    if (!userToUpdate) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'User to update not found.' } 
        },
        { status: 404 }
      );
    }
    
    // Update the user's credits
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'fineTuneCredits.remainingCredits': credits,
          'fineTuneCredits.lastPurchasedCredits': credits - userToUpdate.fineTuneCredits.totalUsed,
          'fineTuneCredits.lastCreditPurchasedAt': new Date()
        }
      },
      { new: true, runValidators: true }
    ).select('-password');
    
    if (!updatedUser) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'Failed to update user credits.' } 
        },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { 
        success: true, 
        data: {
          user: updatedUser,
          updatedBy: authenticatedUser.email,
          message: `User credits updated to ${credits}`
        }
      },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
});
