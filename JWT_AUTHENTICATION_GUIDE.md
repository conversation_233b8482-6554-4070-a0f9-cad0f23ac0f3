# JWT Authentication Implementation Guide

This guide explains how JWT authentication has been implemented in your Next.js application.

## Overview

The JWT authentication system provides:
- Token generation upon successful login
- Token validation for protected routes
- Resource ownership protection
- Automatic token expiration handling

## Files Created/Modified

### 1. JWT Utilities (`src/lib/auth/jwt.ts`)
- `generateToken()` - Creates JWT tokens
- `verifyToken()` - Validates JWT tokens
- `extractTokenFromRequest()` - Extracts tokens from Authorization headers
- `validateTokenFromRequest()` - Complete token validation from request
- `createUnauthorizedResponse()` - Standard 401 response helper

### 2. Authentication Middleware (`src/lib/auth/middleware.ts`)
- `withAuth()` - Wraps routes requiring authentication
- `withAuthAndOwnership()` - Wraps routes requiring authentication + resource ownership
- `checkResourceOwnership()` - Validates user owns the resource

### 3. Updated Routes

#### Login Route (`src/app/api/auth/login/route.ts`)
- Now returns JWT token upon successful authentication
- Token includes user ID and email

#### Protected Routes
- `src/app/api/users/route.ts` - GET method requires authentication
- `src/app/api/users/[id]/resume/route.ts` - All methods require authentication + ownership

## How to Use

### 1. User Login
```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "message": "Login successful"
  }
}
```

### 2. Making Authenticated Requests

Include the JWT token in the Authorization header:

```bash
GET /api/users
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. Resource Ownership Protection

For user-specific routes like `/api/users/[id]/resume`, the system automatically:
1. Validates the JWT token
2. Extracts the user ID from the token
3. Compares it with the `id` parameter in the URL
4. Returns 403 Forbidden if user doesn't own the resource

## Error Responses

### 401 Unauthorized (Missing/Invalid Token)
```json
{
  "success": false,
  "error": {
    "message": "Authentication required. Please provide a valid JWT token."
  }
}
```

### 403 Forbidden (Resource Ownership)
```json
{
  "success": false,
  "error": {
    "message": "Forbidden. You can only access your own resources."
  }
}
```

## Environment Variables

Add these to your `.env.local` file:

```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
```

## Client-Side Implementation

### 1. Token Storage Utilities (`src/lib/auth/token-storage.ts`)
- `storeToken()` - Store JWT token in localStorage
- `getToken()` - Retrieve JWT token from localStorage
- `removeToken()` - Remove JWT token and user data
- `storeUser()` - Store user data in localStorage
- `getUser()` - Retrieve user data from localStorage
- `isAuthenticated()` - Check if user has valid token
- `getAuthHeaders()` - Get authorization headers for API requests
- `authenticatedFetch()` - Make authenticated API requests
- `logout()` - Clear tokens and redirect to login

### 2. Updated Components

#### LoginForm Component (`src/components/LoginForm.tsx`)
- Automatically stores JWT token and user data upon successful login
- Uses token storage utilities for secure client-side storage

#### Login Page (`src/app/login/page.tsx`)
- Checks authentication status on load
- Redirects authenticated users to home page
- Shows loading state while checking authentication

#### AuthStatus Component (`src/components/AuthStatus.tsx`)
- Displays current authentication status
- Shows user name and logout button when authenticated
- Shows login link when not authenticated

#### Main Page (`src/app/page.tsx`)
- Uses authenticated API calls for protected resources
- Loads user data based on authentication status
- Integrates AuthStatus component in header

### 3. Frontend Integration Example

```javascript
// Login and store token (handled automatically by LoginForm)
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { data } = await loginResponse.json();
if (data.token) {
  storeToken(data.token);
  storeUser(data.user);
}

// Use authenticated fetch utility
import { authenticatedFetch } from '@/lib/auth/token-storage';

const response = await authenticatedFetch('/api/users');
const result = await response.json();

// Check authentication status
import { isAuthenticated, getUser } from '@/lib/auth/token-storage';

if (isAuthenticated()) {
  const currentUser = getUser();
  console.log('Current user:', currentUser);
}

// Logout
import { logout } from '@/lib/auth/token-storage';
logout(); // Clears tokens and redirects to login
```

### 4. Authentication Flow

1. **User visits login page**
   - Page checks if already authenticated
   - Redirects to home if already logged in
   - Shows login form if not authenticated

2. **User submits login form**
   - Form sends credentials to `/api/auth/login`
   - Server validates and returns JWT token + user data
   - Client stores token and user data in localStorage
   - User is redirected to home page

3. **User makes API requests**
   - Client automatically includes JWT token in Authorization header
   - Server validates token using middleware
   - Protected routes check resource ownership

4. **User logs out**
   - Client clears token and user data from localStorage
   - User is redirected to login page

## Protecting New Routes

### Simple Authentication
```typescript
import { withAuth } from '@/lib/auth/middleware';

export const GET = withAuth(async (request, context, user) => {
  // user contains: { userId, email, iat, exp }
  // Your route logic here
  return NextResponse.json({ message: 'Protected data', user: user.email });
});
```

### Authentication + Resource Ownership
```typescript
import { withAuthAndOwnership } from '@/lib/auth/middleware';

export const GET = withAuthAndOwnership(
  async (request, context, user) => {
    const { id: resourceUserId } = await context.params;
    // Ownership is automatically validated
    // Your route logic here
    return NextResponse.json({ message: 'User-specific data' });
  },
  (params) => params.id // Function to extract user ID from params
);
```

## Security Considerations

1. **JWT Secret**: Use a strong, random secret in production
2. **Token Expiration**: Tokens expire in 7 days by default
3. **HTTPS**: Always use HTTPS in production
4. **Token Storage**: Store tokens securely on the client side
5. **Refresh Tokens**: Consider implementing refresh tokens for longer sessions

## Testing the Implementation

1. **Create a user** (if not exists):
   ```bash
   POST /api/users
   {
     "name": "Test User",
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **Login to get token**:
   ```bash
   POST /api/auth/login
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

3. **Test protected route**:
   ```bash
   GET /api/users
   Authorization: Bearer YOUR_TOKEN_HERE
   ```

4. **Test resource ownership**:
   ```bash
   GET /api/users/USER_ID/resume
   Authorization: Bearer YOUR_TOKEN_HERE
   ```

The system is now fully implemented and ready for use!
