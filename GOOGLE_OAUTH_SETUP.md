# Google OAuth Setup Guide

This guide will help you set up Google OAuth for your PayDai application.

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Make sure billing is enabled (required for OAuth)

## Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API" and enable it
3. Also enable "Google People API" for better profile information

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace)
3. Fill in the required information:
   - App name: "PayDai"
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
5. Add test users (your email addresses) for testing

## Step 4: Create OAuth Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application"
4. Set the name: "PayDai Web Client"
5. Add authorized redirect URIs:
   - `http://localhost:3050/api/auth/callback/google` (for development)
   - `https://yourdomain.com/api/auth/callback/google` (for production)

## Step 5: Update Environment Variables

1. Copy the Client ID and Client Secret from the credentials page
2. Update your `.env.local` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-actual-google-client-id-here
GOOGLE_CLIENT_SECRET=your-actual-google-client-secret-here

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3050
NEXTAUTH_SECRET=your-secure-random-string-here
```

## Step 6: Generate NextAuth Secret

Generate a secure random string for NEXTAUTH_SECRET:

```bash
openssl rand -base64 32
```

Or use an online generator and replace `your-nextauth-secret-key-here` in `.env.local`.

## Step 7: Test the Integration

1. Start your development server: `npm run dev`
2. Go to `http://localhost:3050/login`
3. Click "Sign in with Google"
4. Complete the OAuth flow
5. Check that the user is created in your MongoDB database

## Important Notes

- Keep your Client Secret secure and never commit it to version control
- For production, make sure to update the authorized redirect URIs
- The OAuth consent screen needs to be verified by Google for production use
- Test users can use the app even if the consent screen is not verified

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**: Make sure the redirect URI in Google Console matches exactly with your app URL
2. **"access_blocked" error**: Add your email as a test user in the OAuth consent screen
3. **"invalid_client" error**: Check that your Client ID and Secret are correct in `.env.local`

### Debug Steps:

1. Check browser console for errors
2. Verify environment variables are loaded correctly
3. Check MongoDB connection and user creation
4. Review NextAuth debug logs by adding `NEXTAUTH_DEBUG=true` to `.env.local`
