import { useState, useEffect, useCallback } from 'react';
import { authenticatedFetch, isAuthenticated, getUser } from '@/lib/auth/token-storage';
import { IUser } from '@/models/User';

interface ParsedResume {
  userId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  htmlContent?: string;
  resumeStructuredData?: any;
  aiParsedResume?: any;
}

interface LoginResponseData {
  user: IUser;
  token: string;
  message: string;
}

export const useAuth = () => {
  const [loading, setLoading] = useState(true);
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  const [existingUser, setExistingUser] = useState<string | null>(null);
  const [error, setError] = useState('');

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);

        // Check if user is authenticated
        if (isAuthenticated()) {
          setIsUserAuthenticated(true);
          const currentUser = getUser();
          if (currentUser && currentUser._id) {
            setExistingUser(currentUser._id);
          }
        } else {
          setIsUserAuthenticated(false);
        }
      } catch (err) {
        console.error('Error loading user data:', err);
        setError(`Failed to load user data: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Handle successful login
  const handleLoginSuccess = (data: LoginResponseData) => {
    setIsUserAuthenticated(true);
    setExistingUser(data.user._id as string);
    setError('');
  };

  // Load user's resume data
  const loadUserResume = useCallback(async (userId: string): Promise<ParsedResume | null> => {
    try {
      const resumeResponse = await authenticatedFetch(`/api/users/${userId}/resume`);
      const resumeResult = await resumeResponse.json();

      if (resumeResult.success) {
        return resumeResult.data;
      }
      return null;
    } catch (err) {
      console.error('Error loading user resume:', err);
      return null;
    }
  }, []);

  return {
    loading,
    isUserAuthenticated,
    existingUser,
    error,
    setError,
    handleLoginSuccess,
    loadUserResume,
  };
};
