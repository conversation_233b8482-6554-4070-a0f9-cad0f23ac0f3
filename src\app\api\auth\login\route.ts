import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import User, { IUser } from '@/models/User';
import { handleApiError } from '@/lib/api/error-handler';
import { comparePassword } from '@/lib/auth/password';
import { generateToken } from '@/lib/auth/jwt';
import mongoose from 'mongoose';

// Define a type for the user response without password
type UserResponseWithoutPassword = Omit<IUser & { _id: mongoose.Types.ObjectId }, 'password'> & {
  password?: string;
};

export async function POST(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get the request body
    const { email, password } = await req.json();
    
    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Email and password are required' 
          } 
        },
        { status: 400 }
      );
    }
    
    // Find the user by email
    const user: IUser | null = await User.findOne({ email });
    
    // Check if user exists
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid email or password' 
          } 
        },
        { status: 401 }
      );
    }
    
    // Check if user has a password set
    if (!user.password) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid email or password' 
          } 
        },
        { status: 401 }
      );
    }

    // Check if password is correct
    const isPasswordValid = await comparePassword(password, user.password);
    
    if (!isPasswordValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid email or password' 
          } 
        },
        { status: 401 }
      );
    }
    
    // Create user data without password
    const userWithoutPassword = user.toObject();
    const userResponse = { ...userWithoutPassword } as UserResponseWithoutPassword;
    delete userResponse.password;
    
    // Generate JWT token
    const token = await generateToken({
      userId: (user._id as mongoose.Types.ObjectId).toString(),
      email: user.email,
      roles: user.roles,
    });
    
    // Return user data with JWT token
    return NextResponse.json(
      { 
        success: true, 
        data: {
          user: userResponse,
          token,
          message: 'Login successful'
        }
      }
    );
  } catch (error) {
    return handleApiError(error);
  }
}
