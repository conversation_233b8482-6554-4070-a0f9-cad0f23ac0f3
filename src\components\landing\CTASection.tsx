'use client';

import React from 'react';
import Link from 'next/link';

export const CTASection = () => {
  return (
    <section style={{ background: 'white', color: 'black' }} className="py-20 px-4">
      <div className="container mx-auto max-w-4xl text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">
          Ready to Land Your Dream Job?
        </h2>
        <p className="text-xl mb-10 max-w-2xl mx-auto">
          Join thousands of successful job seekers who have used PaydAI to
          customize their applications and get more interviews.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/app" className="px-8 py-4 rounded text-lg font-medium text-center" style={{ background: 'black', color: 'white' }}>
            Start Your Free Trial
          </Link>
          {/* <button className="border-2 px-8 py-4 rounded text-lg font-medium" style={{ color: 'black', borderColor: 'black' }}>
            Watch Demo
          </button> */}
        </div>
        <p className="mt-6">
          No credit card required. Cancel anytime.
        </p>
      </div>
    </section>
  );
};
