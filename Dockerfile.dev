FROM node:23-alpine

# Set working directory
WORKDIR /app

# Copy only package.json (excluding package-lock.json)
COPY package.json ./

# Install dependencies with extra flags to ensure all dependencies are installed
RUN npm install

# Copy the rest of the application code
COPY . .

# Expose the port the app will run on
EXPOSE 3050

# Start the application in development mode
CMD ["npm", "run", "dev"]
