import { NextRequest, NextResponse } from 'next/server';
import { ChatService } from '@/lib/ai/chat-service';
import { handleApiError } from '@/lib/api/error-handler';
import { withAuthAndOwnership } from '@/lib/auth/middleware';

export const GET = withAuthAndOwnership(
  async (request: NextRequest, context, _authenticatedUser) => {
    try {
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          { success: false, error: { message: 'Invalid parameters' } },
          { status: 400 }
        );
      }
      const { sessionId } = params;

      // Create chat service instance
      const chatService = new ChatService();

      // Get chat history
      const chatHistory = await chatService.getChatHistory(sessionId);

      return NextResponse.json({
        success: true,
        data: {
          session: chatHistory.session,
          messages: chatHistory.messages,
        },
      });

    } catch (error) {
      console.error('Error getting chat history:', error);
      return handleApiError(error);
    }
  },
  (params) => params.id
);
