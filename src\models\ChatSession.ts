import mongoose, { Schema, Document, Model } from 'mongoose';
import { IChatSession } from '@/types/chat';

// Define the ChatSession schema
const ChatSessionSchema: Schema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    sessionType: {
      type: String,
      required: [true, 'Session type is required'],
      enum: ['resume_finetune', 'general_chat'],
      default: 'general_chat',
    },
    status: {
      type: String,
      required: [true, 'Status is required'],
      enum: ['active', 'completed', 'abandoned'],
      default: 'active',
    },
    context: {
      type: Schema.Types.Mixed,
      default: {},
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for better query performance
ChatSessionSchema.index({ userId: 1, createdAt: -1 });
ChatSessionSchema.index({ status: 1 });
ChatSessionSchema.index({ sessionType: 1 });

// Create and export the ChatSession model
export default (mongoose.models.ChatSession as Model<IChatSession & Document>) || 
  mongoose.model<IChatSession & Document>('ChatSession', ChatSessionSchema);
