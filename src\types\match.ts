// Enums for better type safety
export enum InterviewLikelihood {
  HIGH = 'High',
  MEDIUM = 'Medium',
  LOW = 'Low'
}

export enum ScoreCategory {
  TECHNICAL_SKILLS = 'technical_skills',
  EXPERIENCE_RELEVANCE = 'experience_relevance',
  EDUCATIONAL_QUALIFICATIONS = 'educational_qualifications',
  SOFT_SKILLS_CULTURAL_FIT = 'soft_skills_cultural_fit',
  ACHIEVEMENTS_IMPACT = 'achievements_impact'
}

// Score breakdown interface
export interface ScoreBreakdown {
  technical_skills: number;
  experience_relevance: number;
  educational_qualifications: number;
  soft_skills_cultural_fit: number;
  achievements_impact: number;
}

// Detailed feedback interface
export interface MatchFeedback {
  overall_assessment: string;
  strengths: string[];
  gaps: string[];
  recommendations: string[];
  score_breakdown: ScoreBreakdown;
  ats_optimization_score: number;
  interview_likelihood: string;
}

// Main match result interface
export interface MatchResult {
  score: number;
  feedback: MatchFeedback;
  jobDescription?: string;
}

// Utility functions for UI components
export const getScoreColor = (score: number): string => {
  if (score >= 80) return 'green';
  if (score >= 60) return 'yellow';
  return 'red';
};

export const getScoreColorClass = (score: number): string => {
  if (score >= 80) return 'bg-green-500';
  if (score >= 60) return 'bg-yellow-500';
  return 'bg-red-500';
};

export const getInterviewLikelihoodColor = (likelihood: string): string => {
  switch (likelihood.toLowerCase()) {
    case 'high': return 'green';
    case 'medium': return 'yellow';
    case 'low': return 'red';
    default: return 'gray';
  }
};

export const getInterviewLikelihoodColorClass = (likelihood: string): string => {
  switch (likelihood.toLowerCase()) {
    case 'high': return 'bg-green-100 text-green-800 border-green-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-red-100 text-red-800 border-red-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Type guards for runtime validation
export const isValidMatchResult = (obj: unknown): obj is MatchResult => {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  const candidate = obj as Record<string, unknown>;

  return (
    typeof candidate.score === 'number' &&
    candidate.score >= 0 && candidate.score <= 100 &&
    typeof candidate.feedback === 'object' &&
    candidate.feedback !== null &&
    (() => {
      const feedback = candidate.feedback as Record<string, unknown>;
      return (
        typeof feedback.overall_assessment === 'string' &&
        Array.isArray(feedback.strengths) &&
        Array.isArray(feedback.gaps) &&
        Array.isArray(feedback.recommendations) &&
        typeof feedback.score_breakdown === 'object' &&
        feedback.score_breakdown !== null &&
        typeof feedback.ats_optimization_score === 'number' &&
        typeof feedback.interview_likelihood === 'string'
      );
    })()
  );
};


// Default values and constants
export const DEFAULT_SCORE_BREAKDOWN: ScoreBreakdown = {
  technical_skills: 0,
  experience_relevance: 0,
  educational_qualifications: 0,
  soft_skills_cultural_fit: 0,
  achievements_impact: 0
};

export const SCORE_CATEGORY_LABELS: Record<keyof ScoreBreakdown, string> = {
  technical_skills: 'Technical Skills',
  experience_relevance: 'Experience Relevance',
  educational_qualifications: 'Educational Qualifications',
  soft_skills_cultural_fit: 'Soft Skills & Cultural Fit',
  achievements_impact: 'Achievements & Impact'
};

export const SCORE_CATEGORY_DESCRIPTIONS: Record<keyof ScoreBreakdown, string> = {
  technical_skills: 'Alignment of technical competencies, tools, and methodologies',
  experience_relevance: 'Years of experience, industry alignment, and responsibility scope',
  educational_qualifications: 'Degree requirements, certifications, and specialized training',
  soft_skills_cultural_fit: 'Leadership, communication, teamwork, and adaptability',
  achievements_impact: 'Quantifiable accomplishments and measurable outcomes'
};

// Score category weights (as defined in the C.R.A.F.T. framework)
export const SCORE_WEIGHTS: Record<keyof ScoreBreakdown, number> = {
  technical_skills: 0.25,
  experience_relevance: 0.25,
  educational_qualifications: 0.15,
  soft_skills_cultural_fit: 0.20,
  achievements_impact: 0.15
};
