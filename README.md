# PaydAI - Next.js with MongoDB

This is a [Next.js](https://nextjs.org) project with TypeScript and MongoDB integration, bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Features

- **Next.js 15** with App Router
- **TypeScript** for type safety
- **MongoDB** integration using Mongoose
- **TailwindCSS** for styling
- User management API endpoints
- Form handling with validation
- PDF resume parsing and storage

## Prerequisites

- Node.js 18.17.0 or later
- MongoDB installed locally or a MongoDB Atlas account

## Getting Started

You have two options to run this application:

1. **Standard Setup**: Install dependencies locally and connect to MongoDB
2. **Docker Setup**: Use Docker Compose to run both the application and MongoDB in containers

### Option 1: Standard Setup

#### 1. Set up MongoDB

You have two options for setting up MongoDB:

#### Option 1: Local MongoDB (requires MongoDB installed locally)

If you have MongoDB installed locally, you can use the following connection string in your `.env.local` file:

```
MONGODB_URI=mongodb://localhost:27017/paydai
```

Make sure your local MongoDB server is running before starting the application.

#### Option 2: MongoDB Atlas (recommended for beginners)

MongoDB Atlas is a fully-managed cloud database service that requires no installation:

1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register)
2. Create a new cluster (the free tier is sufficient)
3. In the Security section, create a database user with read and write permissions
4. In the Network Access section, add your IP address or allow access from anywhere for development
5. In the Database section, click "Connect" on your cluster, then "Connect your application"
6. Copy the connection string and replace `<username>`, `<password>`, and `<cluster>` with your actual values
7. Update the `.env.local` file with your MongoDB Atlas connection string:

```
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/paydai?retryWrites=true&w=majority
```

### 2. Install dependencies

```bash
npm install
```

### 3. Seed the database (optional)

To populate the database with sample data:

```bash
npm run seed
```

This will create three sample users in the database.

### 4. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Option 2: Docker Setup (Recommended)

This project includes Docker configuration files to make it easy to run both the Next.js application and MongoDB in containers.

#### Prerequisites
- Docker and Docker Compose installed on your machine

#### Running the Application with Docker Compose

We've included a convenient script to make it easier to run Docker Compose commands:

1. Make the script executable (if not already):

```bash
chmod +x docker-start.sh
```

2. Use the script with one of the following options:

```bash
# Start in development mode with hot reloading
./docker-start.sh dev

# Start in production mode
./docker-start.sh prod

# Seed the database with sample data
./docker-start.sh seed

# Stop the containers
./docker-start.sh down

# Show help
./docker-start.sh help
```

Alternatively, you can use Docker Compose commands directly:

**For development with hot reloading:**

```bash
docker-compose -f docker-compose.dev.yml up
```

**For production-like environment:**

```bash
docker-compose up
```

**Seeding the database:**

```bash
docker-compose exec nextjs-app npm run seed
```

**Stopping the containers:**

```bash
docker-compose down
```

**Removing volumes (will delete MongoDB data):**

```bash
docker-compose down -v
```

The application will be available at http://localhost:3000 after starting the containers.

The application includes a simple user management interface where you can:
- Add new users with name, email, and password
- View a list of all users in the database
- Upload and parse user resumes in PDF format
- Test the application with the sample data if you ran the seed script

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## API Endpoints

### User Management

- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/{id}` - Get a specific user
- `PUT /api/users/{id}` - Update a user
- `DELETE /api/users/{id}` - Delete a user

### Resume Management

- `POST /api/users/{id}/resume` - Upload and parse a user's resume (PDF)
- `GET /api/users/{id}/resume` - Get a user's parsed resume data
- `DELETE /api/users/{id}/resume` - Delete a user's resume

#### Resume Upload Example

To upload a resume for a user, send a POST request with a PDF file to `/api/users/{id}/resume`:

```bash
# Using curl
curl -X POST -F "file=@path/to/resume.pdf" http://localhost:3000/api/users/{id}/resume

# Using the test script (after adding a valid user ID)
npx ts-node src/scripts/test-resume-upload.ts
```

The API will:
1. Parse the PDF document
2. Extract text content
3. Attempt to identify key sections like skills, education, and experience
4. Store the data in the MongoDB `user_profiles` collection
5. Return the parsed data in the response

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
