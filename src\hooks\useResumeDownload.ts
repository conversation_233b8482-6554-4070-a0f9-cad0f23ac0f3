import { useState } from 'react';
import { authenticatedFetch } from '@/lib/auth/token-storage';

export const useResumeDownload = () => {
  const [downloading, setDownloading] = useState(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);

  const downloadResume = async (
    userId: string,
    htmlContent: string,
    fileName?: string
  ): Promise<boolean> => {
    setDownloading(true);
    setDownloadError(null);

    try {
      const response = await authenticatedFetch(`/api/users/${userId}/resume/download`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent,
          fileName: fileName || `resume-${new Date().toISOString().split('T')[0]}.pdf`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to download resume');
      }

      // Get the PDF blob
      const blob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from response headers or use default
      const contentDisposition = response.headers.get('Content-Disposition');
      let downloadFileName = fileName || `resume-${new Date().toISOString().split('T')[0]}.pdf`;
      
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
        if (fileNameMatch) {
          downloadFileName = fileNameMatch[1];
        }
      }
      
      link.download = downloadFileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return true;
    } catch (error) {
      console.error('Download error:', error);
      setDownloadError(error instanceof Error ? error.message : 'Failed to download resume');
      return false;
    } finally {
      setDownloading(false);
    }
  };

  const clearDownloadError = () => {
    setDownloadError(null);
  };

  return {
    downloading,
    downloadError,
    downloadResume,
    clearDownloadError,
  };
};
