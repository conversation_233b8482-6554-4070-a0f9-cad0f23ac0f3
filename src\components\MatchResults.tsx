'use client';

import React from 'react';
import { 
  MatchResult, 
  getScoreColorClass, 
  getInterviewLikelihoodColorClass,
  SCORE_CATEGORY_LABELS,
  SCORE_CATEGORY_DESCRIPTIONS 
} from '@/types/match';

interface MatchResultsProps {
  matchResult: MatchResult;
}

const MatchResults: React.FC<MatchResultsProps> = ({ matchResult }) => {
  const { score, feedback } = matchResult;

  return (
    <div className="space-y-6">
      {/* Overall Score Card */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h4 className="text-xl font-semibold mb-4">Overall Match Score</h4>
        <div className="flex items-center mb-4">
          <div className="w-full bg-gray-200 rounded-full h-6 mr-4">
            <div
              className={`h-6 rounded-full transition-all duration-500 ${getScoreColorClass(score)}`}
              style={{ width: `${score}%` }}
            ></div>
          </div>
          <span className="text-2xl font-bold min-w-[60px]">{score}%</span>
        </div>
        <p className="text-gray-700 text-lg">{feedback.overall_assessment}</p>
      </div>

      {/* Score Breakdown Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(feedback.score_breakdown).map(([key, value]) => (
          <div key={key} className="bg-white rounded-lg shadow-md p-4">
            <h5 className="font-medium text-gray-900 mb-2">
              {SCORE_CATEGORY_LABELS[key as keyof typeof SCORE_CATEGORY_LABELS]}
            </h5>
            <div className="flex items-center mb-2">
              <div className="w-full bg-gray-200 rounded-full h-3 mr-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${getScoreColorClass(value)}`}
                  style={{ width: `${value}%` }}
                ></div>
              </div>
              <span className="font-semibold text-sm min-w-[35px]">{value}%</span>
            </div>
            <p className="text-xs text-gray-600">
              {SCORE_CATEGORY_DESCRIPTIONS[key as keyof typeof SCORE_CATEGORY_DESCRIPTIONS]}
            </p>
          </div>
        ))}
      </div>

      {/* ATS Optimization and Interview Likelihood */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* ATS Optimization Score */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h5 className="text-lg font-semibold mb-3 text-blue-700">ATS Optimization</h5>
          <div className="flex items-center mb-3">
            <div className="w-full bg-gray-200 rounded-full h-4 mr-3">
              <div
                className={`h-4 rounded-full transition-all duration-300 ${getScoreColorClass(feedback.ats_optimization_score)}`}
                style={{ width: `${feedback.ats_optimization_score}%` }}
              ></div>
            </div>
            <span className="font-bold text-lg min-w-[50px]">{feedback.ats_optimization_score}%</span>
          </div>
          <p className="text-sm text-gray-600">
            How well your resume is optimized for Applicant Tracking Systems
          </p>
        </div>

        {/* Interview Likelihood */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h5 className="text-lg font-semibold mb-3 text-purple-700">Interview Likelihood</h5>
          <div className={`inline-block px-4 py-2 rounded-full border text-sm font-medium ${getInterviewLikelihoodColorClass(feedback.interview_likelihood)}`}>
            {feedback.interview_likelihood}
          </div>
          <p className="text-sm text-gray-600 mt-3">
            Probability of getting an interview based on this match
          </p>
        </div>
      </div>

      {/* Detailed Feedback Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Strengths */}
        <div className="bg-green-50 rounded-lg shadow-md p-6 border border-green-200">
          <h5 className="text-lg font-semibold mb-4 text-green-800 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Strengths
          </h5>
          <ul className="space-y-2">
            {feedback.strengths.map((strength, index) => (
              <li key={index} className="text-sm text-green-700 flex items-start">
                <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {strength}
              </li>
            ))}
          </ul>
        </div>

        {/* Gaps */}
        <div className="bg-red-50 rounded-lg shadow-md p-6 border border-red-200">
          <h5 className="text-lg font-semibold mb-4 text-red-800 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Areas for Improvement
          </h5>
          <ul className="space-y-2">
            {feedback.gaps.map((gap, index) => (
              <li key={index} className="text-sm text-red-700 flex items-start">
                <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {gap}
              </li>
            ))}
          </ul>
        </div>

        {/* Recommendations */}
        <div className="bg-blue-50 rounded-lg shadow-md p-6 border border-blue-200">
          <h5 className="text-lg font-semibold mb-4 text-blue-800 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
            </svg>
            Recommendations
          </h5>
          <ul className="space-y-2">
            {feedback.recommendations.map((recommendation, index) => (
              <li key={index} className="text-sm text-blue-700 flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {recommendation}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MatchResults;
