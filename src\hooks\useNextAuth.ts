'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect, useCallback } from 'react';

export const useNextAuth = () => {
  const { data: session, status } = useSession();
  const [existingUser, setExistingUser] = useState<string | null>(null);
  const [error, setError] = useState<string>('');

  const loading = status === 'loading';
  const isUserAuthenticated = status === 'authenticated' && !!session;

  useEffect(() => {
    if (session?.userId) {
      setExistingUser(session.userId);
    } else {
      setExistingUser(null);
    }
  }, [session?.userId]);

  const loadUserResume = useCallback(async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/resume`);

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          return result.data;
        } else {
          console.error('Failed to load user resume - invalid response format');
          return null;
        }
      } else {
        console.error('Failed to load user resume - HTTP error:', response.status);
        return null;
      }
    } catch (error) {
      console.error('Error loading user resume:', error);
      return null;
    }
  }, []);

  return {
    loading,
    isUserAuthenticated,
    existingUser,
    error,
    setError,
    loadUserResume,
    session,
  };
};
