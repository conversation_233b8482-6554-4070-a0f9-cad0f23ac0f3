import { NextRequest, NextResponse } from 'next/server';
import { ChatService } from '@/lib/ai/chat-service';
import { handleApiError } from '@/lib/api/error-handler';
import { MatchResult, isValidMatchResult } from '@/types/match';
import UserProfile from '@/models/UserProfile';
import User from '@/models/User';
import connectToDatabase from '@/lib/mongodb';
import { withAuthAndOwnership } from '@/lib/auth/middleware';

export const POST = withAuthAndOwnership(
  async (request: NextRequest, context, _authenticatedUser) => {
    try {
      const params = await context.params;
      if (!params) {
        return NextResponse.json(
          { success: false, error: { message: 'Invalid parameters' } },
          { status: 400 }
        );
      }
      const { id: userId } = params;
      const body = await request.json();
      const { context: { matchResult }  } = body;

      // Validate required fields
      if (!matchResult || !isValidMatchResult(matchResult)) {
        return NextResponse.json(
          { success: false, error: { message: 'Valid match result is required' } },
          { status: 400 }
        );
      }

      // Connect to database
      await connectToDatabase();

      // Check user's fine-tune credits
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          { success: false, error: { message: 'User not found' } },
          { status: 404 }
        );
      }

      // Check if user has remaining credits
      if (user.fineTuneCredits.remainingCredits <= 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: { 
              message: 'No fine-tune credits remaining. Please purchase more credits to continue.',
              code: 'INSUFFICIENT_CREDITS'
            },
            data: {
              remainingCredits: user.fineTuneCredits.remainingCredits,
              totalUsed: user.fineTuneCredits.totalUsed
            }
          },
          { status: 402 } // Payment Required
        );
      }

      // Query user profile
      const userProfile = await UserProfile.findOne({ user: userId });
      
      if (!userProfile || !userProfile.resumes || userProfile.resumes.length === 0) {
        return NextResponse.json(
          { success: false, error: { message: 'User profile or resume not found' } },
          { status: 404 }
        );
      }

      // Get the latest resume (last one added)
      const latestResume = userProfile.resumes[userProfile.resumes.length - 1];

      if (!latestResume?.rawText) {
        return NextResponse.json(
          { success: false, error: { message: 'Resume data not found in user profile' } },
          { status: 404 }
        );
      }

      // Deduct credit at session creation (when fine-tuning starts)
      const now = new Date();
      user.fineTuneCredits.remainingCredits -= 1;
      user.fineTuneCredits.totalUsed += 1;
      user.fineTuneCredits.lastUsedAt = now;
      
      // Set firstUsedAt if this is the first time
      if (!user.fineTuneCredits.firstUsedAt) {
        user.fineTuneCredits.firstUsedAt = now;
      }

      await user.save();

      // Create chat service instance
      const chatService = new ChatService();

      // Create the chat session using resume raw text from latest resume
      const sessionData = await chatService.createResumeFineTuneSession(
        userId,
        latestResume.rawText,
        matchResult as MatchResult
      );

      return NextResponse.json({
        success: true,
        data: {
          sessionId: sessionData.sessionId,
          initialMessage: sessionData.firstQuestion,
          context: {
            analysis: sessionData.analysis,
            matchScore: matchResult.score,
          },
          creditsRemaining: user.fineTuneCredits.remainingCredits,
          totalUsed: user.fineTuneCredits.totalUsed
        },
      });

    } catch (error) {
      console.error('Error creating chat session:', error);
      return handleApiError(error);
    }
  },
  (params) => params.id
);
