/**
 * Example usage of the Resume Parser Service
 * This file demonstrates how to use the markdown-based resume parsing functionality
 */

import { parseResumeText, ResumeParserService } from './resume-parser-service';
import { ParsedResumeResponse } from './prompt-reader';

/**
 * Example function showing how to use the resume parser
 */
export async function exampleResumeParsingUsage() {
  // Sample resume text (this would typically come from PDF extraction)
  const sampleResumeText = `
    <PERSON>
    Software Engineer
    <EMAIL>
    (555) 123-4567
    LinkedIn: linkedin.com/in/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 5+ years in full-stack development.
    
    SKILLS
    Programming Languages: JavaScript, Python, Java
    Frameworks: React, Node.js, Django
    Databases: PostgreSQL, MongoDB
    
    WORK EXPERIENCE
    Senior Software Engineer
    Tech Company Inc. | San Francisco, CA
    January 2020 - Present
    • Developed and maintained web applications using React and Node.js
    • Led a team of 3 junior developers
    • Improved application performance by 40%
    
    Software Engineer
    StartupXYZ | San Francisco, CA
    June 2018 - December 2019
    • Built RESTful APIs using Python and Django
    • Collaborated with cross-functional teams
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of California, Berkeley
    Graduated: May 2018
    GPA: 3.8/4.0
    
    CERTIFICATIONS
    AWS Certified Developer
    Amazon Web Services
    Issued: March 2021
    Expires: March 2024
  `;

  try {
    console.log('Parsing resume...');
    
    // Method 1: Using the convenience function
    const parsedResume: ParsedResumeResponse = await parseResumeText(sampleResumeText);
    
    console.log('Parsed Resume Data:');
    console.log('Name:', parsedResume.personal_details.name);
    console.log('Email:', parsedResume.personal_details.email_addresses);
    console.log('Phone:', parsedResume.personal_details.phone_numbers);
    console.log('Skills:', parsedResume.skills);
    console.log('Work Experience:', parsedResume.work_experience.length, 'positions');
    console.log('Education:', parsedResume.education.length, 'entries');
    
    return parsedResume;
    
  } catch (error) {
    console.error('Error parsing resume:', error);
    throw error;
  }
}

/**
 * Example function showing how to use the ResumeParserService class directly
 */
export async function exampleUsingServiceClass(resumeText: string) {
  const parserService = new ResumeParserService();
  
  try {
    // Parse the resume
    const result = await parserService.parseResume(resumeText);
    return result;
  } catch (error) {
    console.warn('AI parsing failed, generating fallback response');
    // Use fallback if AI parsing fails
    return parserService.generateFallbackResponse(resumeText);
  }
}

/**
 * Example function to demonstrate integration with existing PDF parsing workflow
 */
export async function integrateWithPdfParser(pdfText: string): Promise<ParsedResumeResponse> {
  // This would typically be called after extracting text from a PDF
  // For example, after using pdf-parser.ts to extract text from a PDF file
  
  console.log('Integrating with PDF parser workflow...');
  
  // Clean up the PDF text if needed (remove extra whitespace, etc.)
  const cleanedText = pdfText
    .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
    .replace(/\n\s*\n/g, '\n')  // Remove empty lines
    .trim();
  
  // Parse the cleaned text
  const parsedResume = await parseResumeText(cleanedText);
  
  console.log('Successfully parsed resume from PDF text');
  return parsedResume;
}

/**
 * Utility function to validate parsed resume data
 */
export function validateParsedResume(parsedResume: ParsedResumeResponse): boolean {
  // Basic validation checks
  const hasName = !!(parsedResume.personal_details.name && parsedResume.personal_details.name.length > 0);
  const hasContact = parsedResume.personal_details.email_addresses.length > 0 || 
                    parsedResume.personal_details.phone_numbers.length > 0;
  
  if (!hasName) {
    console.warn('Validation warning: No name found in parsed resume');
  }
  
  if (!hasContact) {
    console.warn('Validation warning: No contact information found in parsed resume');
  }
  
  return hasName && hasContact;
}

/**
 * Example of how this would be used in an API route
 */
export async function apiRouteExample(resumeText: string) {
  try {
    // Parse the resume
    const parsedResume = await parseResumeText(resumeText);
    
    // Validate the result
    const isValid = validateParsedResume(parsedResume);
    
    if (!isValid) {
      console.warn('Parsed resume may be incomplete');
    }
    
    // Return the structured data (this would be sent as JSON response)
    return {
      success: true,
      data: parsedResume,
      isValid: isValid
    };
    
  } catch (error) {
    console.error('API route error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    };
  }
}
