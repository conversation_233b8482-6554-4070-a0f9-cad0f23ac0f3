'use client';

import { useState, useEffect } from 'react';
import { isAuthenticated, getUser, logout } from '@/lib/auth/token-storage';
import { IUser } from '@/models/User';

const AuthStatus: React.FC = () => {
  const [user, setUser] = useState<IUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAuthenticated()) {
      const storedUser = getUser();
      setUser(storedUser);
    }
    setLoading(false);
  }, []);

  const handleLogout = () => {
    logout();
    setUser(null);
  };

  if (loading) {
    return <div className="text-gray-500">Loading...</div>;
  }

  if (user) {
    return (
      <div className="flex items-center space-x-4">
        <span className="text-gray-700">Welcome, {user.name}!</span>
        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Logout
        </button>
      </div>
    );
  }

  return null;
};

export default AuthStatus;
