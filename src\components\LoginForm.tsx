'use client';

import React, { useState, useEffect } from 'react';
import { signIn, useSession } from 'next-auth/react';
import LoadingSpinner from './LoadingSpinner';
import { IUser } from '@/models/User';
import { storeToken, storeUser } from '@/lib/auth/token-storage';

interface LoginResponseData {
  user: IUser;
  token: string;
  message: string;
}

interface LoginFormProps {
  onSuccess?: (data: LoginResponseData) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const { data: session } = useSession();
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    setError('');
    setSuccess('');
    setGoogleLoading(true);
    
    try {
      const result = await signIn('google', { 
        redirect: false,
        callbackUrl: '/app'
      });
      
      if (result?.error) {
        setError('Google sign-in failed');
      }
    } catch (err) {
      setError('An error occurred during Google sign-in');
      console.error(err);
    } finally {
      setGoogleLoading(false);
    }
  };


  // Handle successful Google authentication
  useEffect(() => {
    if (session?.customToken && !googleLoading) {
      // Store the custom JWT token from Google login
      storeToken(session.customToken);
      
      // Create user object from session data
      const userData = {
        _id: session.userId,
        name: session.user?.name || '',
        email: session.user?.email || '',
        roles: session.roles || ['user'],
        provider: 'google',
        avatar: session.user?.image,
      };
      
      storeUser(userData as any);
      
      setSuccess('Google login successful!');
      
      if (onSuccess) {
        onSuccess({
          user: userData as any,
          token: session.customToken,
          message: 'Google login successful'
        });
      } else {
        // Default redirect to app page if no onSuccess callback
        window.location.href = '/app';
      }
    }
  }, [session, googleLoading, onSuccess]);

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-6 text-center">Sign in to PayDai</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">
          {success}
        </div>
      )}
      
      <button
        onClick={handleGoogleSignIn}
        className="w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors flex justify-center items-center shadow-sm"
        disabled={googleLoading}
      >
        {googleLoading ? (
          <>
            <LoadingSpinner size="small" color="text-gray-700" />
            <span className="ml-2">Signing in with Google...</span>
          </>
        ) : (
          <>
            <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </>
        )}
      </button>
      
      <p className="text-sm text-gray-500 text-center mt-4">
        Sign in securely with your Google account
      </p>
    </div>
  );
};

export default LoginForm;
