'use client';

import React, { useCallback, useState } from 'react';
import { CheckIcon, XIcon } from 'lucide-react';
import Link from 'next/link';
import { useCashfreePayment } from '@/hooks/useCashfreePayment';
import { useNextAuth } from '@/hooks/useNextAuth';
import PaymentModal from '@/components/PaymentModal';

export const Pricing = () => {
  const { existingUser, session } = useNextAuth();
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPlanAmount, setSelectedPlanAmount] = useState<number | string>(0);
  const [paymentStatus, setPaymentStatus] = useState<string>('');
  const [paymentError, setPaymentError] = useState<string>('');

  const handlePlanSelection = useCallback((planAmount: number | string) => {
    setSelectedPlanAmount(planAmount);
    setPaymentStatus('');
    setPaymentError('');
    setIsPaymentModalOpen(true);
  }, []);

  const handlePaymentSuccess = useCallback(() => {
    setPaymentStatus('Payment completed successfully!');
    // You could redirect to success page or show success message
    setTimeout(() => setPaymentStatus(''), 5000);
  }, []);

  const handlePaymentError = useCallback((error: string) => {
    setPaymentError(error);
    setTimeout(() => setPaymentError(''), 5000);
  }, []);

  const plans = [
    {
      name: 'Free Trial',
      price: '0',
      period: '2 Credits/ 7 days',
      description: 'Perfect for trying out the platform',
      isComingSoon: false,
      launchPrice: null,
      inBlue: ['period'],
      features: [
        {
          included: true,
          text: 'Job matching algorithm'
        },
        {
          included: true,
          text: 'Basic resume customization'
        },
        {
          included: false,
          text: 'Simple cover letter templates',
          isInRed: true
        },
        {
          included: false,
          text: 'Advanced AI suggestions',
        },
        {
          included: false,
          text: 'Unlimited applications'
        },
        {
          included: false,
          text: 'Interview preparation tools'
        }
      ],
      buttonText: 'Start Free Trial',
      highlighted: false
    },
    {
      name: 'Premium',
      price: '699',
      period: '10 Credits / Launch price ',
      description: 'Everything you need to land your dream job',
      isComingSoon: false,
      launchPrice: 99,
      inBlue: ['launchPrice', 'period'],
      features: [
        {
          included: true,
          text: 'Advanced job matching algorithm'
        },
        {
          included: true,
          text: 'Full resume customization'
        },
        {
          included: true,
          text: 'Advanced cover letter creation',
          isComingSoon: true
        },
        {
          included: true,
          text: 'AI-powered suggestions'
        },
        {
          included: true,
          text: 'Unlimited applications'
        },
        {
          included: true,
          text: 'Interview preparation tools',
          isComingSoon: true
        }
      ],
      buttonText: 'Get Premium',
      highlighted: true
    },
    {
      name: 'Annual',
      price: '499',
      period: 'per month, billed annually',
      description: 'Best value for serious job seekers',
      isComingSoon: true,
      launchPrice: null,
      inBlue: [],
      features: [
        {
          included: true,
          text: 'All Premium features'
        },
        {
          included: true,
          text: 'Priority support'
        },
        {
          included: true,
          text: 'Career coaching session'
        },
        {
          included: true,
          text: 'Resume review by experts'
        },
        {
          included: true,
          text: 'LinkedIn profile optimization'
        },
        {
          included: true,
          text: '25% discount vs monthly'
        }
      ],
      // buttonText: 'Save With Annual',
      buttonText: 'Comming Soon',
      highlighted: false
    }
  ];

  return (
    <section id="pricing" style={{ background: 'white', color: 'black' }} className="py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl max-w-3xl mx-auto">
            Choose the plan that fits your needs and start landing more
            interviews today.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div 
              key={index} 
              className="rounded-lg p-8 border flex flex-col" style={{ background: 'white', color: 'black', borderColor: 'black' }}
            >
              <h3 className="text-2xl font-bold mb-2">
                {plan.name} {plan.isComingSoon ? <span className="text-blue-500 text-sm">(Coming Soon)</span> : ''}
              </h3>
              <div className="flex items-baseline mb-2">
                {plan.launchPrice ? (
                  <>
                    <span className="line-through text-4xl font-bold">₹{plan.price}</span>
                    <span className={`${plan.inBlue.includes('launchPrice') ? 'text-blue-500' : ''} text-4xl font-bold ml-2`}>₹{plan.launchPrice}</span>
                    <span className={`${plan.inBlue.includes('period') ? 'text-blue-500' : ''} ml-2`}>/{plan.period}</span>
                  </>
                ) : (
                  <span className="text-4xl font-bold ">₹{plan.price}</span>
                )}
                {!plan.launchPrice && <span className={`${plan.inBlue.includes('period') ? 'text-blue-500' : ''} ml-2`}>/{plan.period}</span>}
              </div>
              <p className="mb-6">{plan.description}</p>
              
              <div className="space-y-3 mb-8">
                {plan.features.map((feature, i) => (
                  <div key={i} className="flex items-center">
                    {feature.included ? (
                      <CheckIcon className="h-5 w-5 mr-3 flex-shrink-0" style={{ color: (feature as any).isInRed ? 'red' : 'black' }} />
                    ) : (
                      <XIcon className="h-5 w-5 mr-3 flex-shrink-0" style={{ color: (feature as any).isInRed ? 'red' : '#ccc' }} />
                    )}
                    <span>
                      {feature.text} {(feature as any).isComingSoon && <span className="text-blue-500 text-sm">(coming soon)</span>}
                    </span>
                  </div>
                ))}
              </div>
              
              {plan.name === 'Premium' ? (
                <button 
                  onClick={() => handlePlanSelection(plan.launchPrice ?? plan.price)}
                  className={`w-full py-3 rounded font-medium block text-center mt-auto ${plan.isComingSoon ? 'opacity-50 cursor-not-allowed ' : ''}`}
                  style={{ background: 'black', color: 'white', pointerEvents: plan.isComingSoon ? 'none' : 'auto' }}
                >
                  {plan.buttonText}
                </button>
              ) : (
                <Link 
                  href={plan.isComingSoon ? "#" : "/app"}
                  className={`w-full py-3 rounded font-medium block text-center mt-auto ${plan.isComingSoon ? 'opacity-50 cursor-not-allowed ' : ''}`}
                  style={{ background: 'black', color: 'white', pointerEvents: plan.isComingSoon ? 'none' : 'auto' }}
                >
                  {plan.buttonText}
                </Link>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Status and error messages */}
      {paymentStatus && (
        <div className="mt-4 text-center text-sm text-green-600" role="status">{paymentStatus}</div>
      )}
      {paymentError && (
        <div className="mt-2 text-center text-sm text-red-600">{paymentError}</div>
      )}

      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
        amount={selectedPlanAmount}
      />
    </section>
  );
};
