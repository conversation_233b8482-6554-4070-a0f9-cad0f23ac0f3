import React from 'react';
import Image from 'next/image';

interface QRCodePaymentProps {
  userId: string;
  onClose: () => void;
  onPaymentSuccess: () => void;
}

export default function QRCodePayment({ userId, onClose, onPaymentSuccess }: QRCodePaymentProps) {
  // Use local QR code image from the qr-code-images folder
  const qrCodeImagePath = "/qr-code-images/payment-qr.png";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 max-w-sm w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Buy More Credits</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        <div className="text-center">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Purchase 5 Credits
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Scan the QR code to pay ₹ 99 and get more credits.
            </p>
          </div>

          {/* QR Code Display */}
          <div className="mb-4 p-3 border-2 border-dashed border-gray-300 rounded-lg">
            <Image
              src={qrCodeImagePath}
              alt="Payment QR Code"
              className="mx-auto mb-2"
              width={150}
              height={150}
            />
            <p className="text-xs text-gray-500">
              Scan with your payment app
            </p>
          </div>

          {/* Payment Instructions */}
          <div className="text-left mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2 text-sm">Instructions:</h4>
            <ol className="list-decimal list-inside space-y-1 text-xs text-gray-700">
              <li>Scan the QR code with your payment app</li>
              <li>Make sure to mention your email/Login ID in UPI comments</li>
              <li>Complete the ₹ 99 payment</li>
              <li>our credits will be added automatically within 1 hour (between 8am-10pm)</li>
              <li>Close this window when done</li>
              <li><NAME_EMAIL> for any issues</li>
            </ol>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
