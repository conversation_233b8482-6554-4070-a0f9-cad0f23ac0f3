import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import FormData from 'form-data';

/**
 * Test script for the resume upload endpoint
 * 
 * Usage:
 * 1. Create a sample PDF file named 'sample-resume.pdf' in the project root
 * 2. Run this script with: npx ts-node src/scripts/test-resume-upload.ts
 */

// Configuration
const API_URL = 'http://localhost:3000/api/users';
const PDF_PATH = path.join(process.cwd(), 'sample-resume.pdf');
const USER_ID = ''; // Add a valid user ID here before running

async function testResumeUpload() {
  if (!USER_ID) {
    console.error('Please add a valid user ID in the script before running');
    process.exit(1);
  }

  try {
    // Check if the sample PDF exists
    if (!fs.existsSync(PDF_PATH)) {
      console.error(`Sample PDF not found at ${PDF_PATH}`);
      console.error('Please create a sample PDF file named "sample-resume.pdf" in the project root');
      process.exit(1);
    }

    // Create form data with the PDF file
    const form = new FormData();
    form.append('file', fs.createReadStream(PDF_PATH));

    // Send the request
    console.log(`Uploading resume for user ${USER_ID}...`);
    const response = await fetch(`${API_URL}/${USER_ID}/resume`, {
      method: 'POST',
      body: form,
    });

    // Parse the response
    const result = await response.json();
    
    // Display the result
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('Resume uploaded successfully!');
    } else {
      console.error('Failed to upload resume');
    }
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testResumeUpload();
