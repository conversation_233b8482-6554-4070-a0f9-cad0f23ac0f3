'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoginForm from '@/components/LoginForm';
import Link from 'next/link';
import { IUser } from '@/models/User';
import { isAuthenticated, getUser } from '@/lib/auth/token-storage';

// Define the login response data type
interface LoginResponseData {
  user: IUser;
  token: string;
  message: string;
}

export default function LoginPage() {
  const router = useRouter();
  const [user, setUser] = useState<IUser>();
  const [loading, setLoading] = useState(true);

  // Check if user is already authenticated
  useEffect(() => {
    if (isAuthenticated()) {
      const storedUser = getUser();
      if (storedUser) {
        setUser(storedUser);
        // Redirect to home page if already logged in
        router.push('/');
        return;
      }
    }
    setLoading(false);
  }, [router]);

  const handleLoginSuccess = (data: LoginResponseData) => {
    setUser(data.user);
    
    // Redirect to home page after a short delay
    setTimeout(() => {
      router.push('/');
    }, 1500);
  };

  // Show loading while checking authentication
  if (loading) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="max-w-md w-full text-center">
          <h1 className="text-4xl font-bold mb-8">PayDai App</h1>
          <p>Checking authentication...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="max-w-md w-full">
        <h1 className="text-4xl font-bold mb-8 text-center">PayDai App</h1>
        
        {user ? (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold mb-4">Welcome, {user.name}!</h2>
            <p className="mb-4">You have successfully logged in.</p>
            <p className="text-sm text-gray-500">Redirecting to home page...</p>
          </div>
        ) : (
          <LoginForm onSuccess={handleLoginSuccess} />
        )}
        
        <div className="mt-4 text-center">
          <Link href="/" className="text-blue-500 hover:text-blue-700">
            Back to Home
          </Link>
        </div>
      </div>
    </main>
  );
}
