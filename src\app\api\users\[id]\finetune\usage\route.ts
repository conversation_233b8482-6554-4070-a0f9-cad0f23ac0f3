import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api/error-handler';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: userId } = await params;

    // Connect to database
    await connectToDatabase();

    // Get user's credit information
    const user = await User.findById(userId).select('fineTuneCredits');
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: { message: 'User not found' } },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        remainingCredits: user.fineTuneCredits.remainingCredits,
        totalUsed: user.fineTuneCredits.totalUsed,
        lastPurchasedCredits: user.fineTuneCredits.lastPurchasedCredits,
        firstUsedAt: user.fineTuneCredits.firstUsedAt,
        lastUsedAt: user.fineTuneCredits.lastUsedAt,
        canFineTune: user.fineTuneCredits.remainingCredits > 0,
        isFirstTime: user.fineTuneCredits.totalUsed === 0
      }
    });

  } catch (error) {
    console.error('Error fetching fine-tune usage:', error);
    return handleApiError(error);
  }
}
