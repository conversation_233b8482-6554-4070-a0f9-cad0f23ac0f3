'use client';

import LoadingSpinner from '@/components/LoadingSpinner';
import { AppHeader } from '@/components/AppHeader';
import LoginForm from '@/components/LoginForm';
import ChatInterface from '@/components/ChatInterface';
import PdfPreview from '@/components/PdfPreview';
import ParsedResumeDisplay from '@/components/ParsedResumeDisplay';
import ResumeUploadSection from '@/components/resume/ResumeUploadSection';
import JobMatchingSection from '@/components/JobMatchingSection';
import HtmlResumePreviewSection from '@/components/HtmlResumePreviewSection';
import { useAppPageLogic } from '@/hooks/useAppPageLogic';

export default function AppPage() {
  const {
    wantsNewUpload,
    authHook,
    resumeHook,
    jobMatchingHook,
    editorHook,
    downloadHook,
    creditsHook,
    handleEnhancedResumeUpload,
    handleEnhancedMatch,
    handleEnhancedFineTune,
    handleBuyMoreCredits,
    handleEnhancedReset,
    getHtmlContent,
  } = useAppPageLogic();

  const {
    loading,
    isUserAuthenticated,
    existingUser,
    error,
  } = authHook;

  const { parsedResume } = resumeHook;

  return (
    <div className="min-h-screen bg-gray-50">
      <AppHeader />
      <main className="flex flex-col items-center p-8 md:p-24">
        <div className="max-w-6xl w-full">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-4xl font-bold">Resume Builder</h1>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : !isUserAuthenticated ? (
            <div className="max-w-md mx-auto">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold mb-4">Welcome to Resume Builder</h2>
                <p className="text-gray-600 mb-6">
                  Please log in to upload your resume and access all features including job matching and AI-powered resume optimization.
                </p>
              </div>
              <LoginForm onSuccess={() => {}} />
            </div>
          ) : !parsedResume ? (
            <ResumeUploadSection
              resumeFile={resumeHook.resumeFile}
              uploading={resumeHook.uploading}
              error={error}
              fileInputRef={resumeHook.fileInputRef}
              onFileChange={resumeHook.handleFileChange}
              onSubmit={handleEnhancedResumeUpload}
            />
          ) : (
            <div className="space-y-6">
              {/* PDF Preview Section */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h2 className="text-2xl font-semibold">Latest Resume Preview</h2>
                    <p className="text-sm text-gray-600 mt-1">
                      Uploaded on {new Date(parsedResume.uploadDate).toLocaleDateString()} at {new Date(parsedResume.uploadDate).toLocaleTimeString()}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleEnhancedReset}
                      className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded transition-colors"
                    >
                      Upload New Resume
                    </button>
                  </div>
                </div>

                <PdfPreview
                  userId={existingUser!}
                  fileName={parsedResume.fileName}
                  fileSize={parsedResume.fileSize}
                  uploadDate={parsedResume.uploadDate}
                />
              </div>

              {/* Parsed Resume Data Display */}
              {parsedResume && (
                <div className="p-6 bg-white rounded-lg shadow-md mb-6">
                  <h3 className="text-xl font-medium text-gray-800 mb-4">Resume Information</h3>
                 
                  {parsedResume.aiParsedResume ? (
                    <ParsedResumeDisplay data={parsedResume.aiParsedResume} />
                  ) : (
                    <div className="text-gray-500 text-center py-8">
                      <p>AI-parsed resume data is not available.</p>
                    </div>
                  )}
                </div>
              )}

              {/* Job Matching Section */}
              <JobMatchingSection
                inputType={jobMatchingHook.inputType}
                jobDescription={jobMatchingHook.jobDescription}
                jobUrl={jobMatchingHook.jobUrl}
                matchLoading={jobMatchingHook.matchLoading}
                matchResult={jobMatchingHook.matchResult}
                error={error}
                creditsData={creditsHook.creditsData}
                creditsLoading={creditsHook.loading}
                creditsError={creditsHook.error}
                onInputTypeChange={jobMatchingHook.handleInputTypeChange}
                onJobDescriptionChange={jobMatchingHook.handleJobDescriptionChange}
                onJobUrlChange={jobMatchingHook.handleJobUrlChange}
                onMatch={handleEnhancedMatch}
                onFineTune={handleEnhancedFineTune}
                onBuyCredits={handleBuyMoreCredits}
              />

              {/* HTML Resume Preview Section */}
              <HtmlResumePreviewSection
                parsedResume={parsedResume}
                editorContent={editorHook.editorContent}
                fineTuneChanges={editorHook.fineTuneChanges}
                downloading={downloadHook.downloading}
                downloadError={downloadHook.downloadError}
                existingUser={existingUser!}
                getHtmlContent={getHtmlContent}
                onDownload={downloadHook.downloadResume}
                onClearDownloadError={downloadHook.clearDownloadError}
              />
            </div>
          )}

          {/* Chat Interface */}
          {editorHook.showChat && existingUser && jobMatchingHook.matchResult && parsedResume && (
            <div className="mt-6">
              <ChatInterface
                userId={existingUser}
                matchResult={jobMatchingHook.matchResult}
                resumeHtml={editorHook.editorContent || getHtmlContent()}
                jobDescription={jobMatchingHook.getJobDescriptionForChat()}
                onClose={editorHook.handleChatClose}
                onComplete={editorHook.handleChatComplete}
              />
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
