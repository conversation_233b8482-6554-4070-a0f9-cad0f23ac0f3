import mongoose, { Schema, Document, Model } from 'mongoose';

// Define the Transaction interface
export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId; // Reference to User
  orderId: string; // Cashfree order ID
  paymentSessionId?: string; // Cashfree payment session ID
  amount: number; // Transaction amount in INR (smallest currency unit, paise)
  currency: string; // Currency code (default: INR)
  status: 'ACTIVE' | 'PAID' | 'EXPIRED' | 'TERMINATED' | 'TERMINATION_REQUESTED' | 'PENDING';
  paymentMethod?: string; // Payment method used (card, upi, netbanking, etc.)
  paymentGateway: 'cashfree' | 'other'; // Payment gateway used
  gatewayResponse?: any; // Raw response from payment gateway
  customerDetails: {
    customerId: string;
    customerEmail: string;
    customerPhone?: string;
    customerName?: string;
  };
  orderDetails?: {
    orderAmount: number;
    orderCurrency: string;
    orderTags?: string[];
  };
  paymentDetails?: {
    cfPaymentId?: string; // Cashfree payment ID
    paymentTime?: Date;
    paymentMessage?: string;
    bankReference?: string;
    authId?: string;
  };
  metadata?: Record<string, any>; // Additional custom data
  createdAt: Date;
  updatedAt: Date;
}

// Define the Transaction schema
const TransactionSchema: Schema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true,
    },
    orderId: {
      type: String,
      required: [true, 'Order ID is required'],
      unique: true,
      index: true,
    },
    paymentSessionId: {
      type: String,
      index: true,
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [1, 'Amount must be at least 1'],
    },
    currency: {
      type: String,
      default: 'INR',
      enum: ['INR', 'USD', 'EUR'],
    },
    status: {
      type: String,
      enum: ['ACTIVE', 'PAID', 'EXPIRED', 'TERMINATED', 'TERMINATION_REQUESTED', 'PENDING'],
      default: 'PENDING',
      required: true,
      index: true,
    },
    paymentMethod: {
      type: String,
      enum: ['card', 'upi', 'netbanking', 'wallet', 'paylater', 'emi'],
    },
    paymentGateway: {
      type: String,
      enum: ['cashfree', 'other'],
      default: 'cashfree',
      required: true,
    },
    gatewayResponse: {
      type: Schema.Types.Mixed, // Can store any object
    },
    customerDetails: {
      customerId: {
        type: String,
        required: true,
      },
      customerEmail: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
      },
      customerPhone: {
        type: String,
        trim: true,
      },
      customerName: {
        type: String,
        trim: true,
      },
    },
    orderDetails: {
      orderAmount: {
        type: Number,
      },
      orderCurrency: {
        type: String,
        default: 'INR',
      },
      orderTags: [String],
    },
    paymentDetails: {
      cfPaymentId: {
        type: String,
        index: true,
      },
      paymentTime: {
        type: Date,
      },
      paymentMessage: {
        type: String,
      },
      bankReference: {
        type: String,
      },
      authId: {
        type: String,
      },
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);


// Create and export the Transaction model
export default (mongoose.models.Transaction as Model<ITransaction>) ||
  mongoose.model<ITransaction>('Transaction', TransactionSchema);