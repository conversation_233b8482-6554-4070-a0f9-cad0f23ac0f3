import { ChatService } from '@/lib/ai/chat-service';
import { MatchResult } from '@/types/match';
import connectToDatabase from '@/lib/mongodb';

// Sample match result for testing
const sampleMatchResult: MatchResult = {
  score: 75,
  feedback: {
    overall_assessment: "Your resume shows good technical skills but lacks quantifiable achievements and could better align with the job requirements.",
    strengths: [
      "Strong technical background in software development",
      "Relevant programming languages mentioned",
      "Good educational background"
    ],
    gaps: [
      "Missing quantifiable achievements and metrics",
      "Limited leadership experience mentioned",
      "Could better highlight project management skills"
    ],
    recommendations: [
      "Add specific metrics to your achievements",
      "Highlight leadership and team collaboration",
      "Include more details about project outcomes"
    ],
    score_breakdown: {
      technical_skills: 85,
      experience_relevance: 70,
      educational_qualifications: 80,
      soft_skills_cultural_fit: 65,
      achievements_impact: 60
    },
    ats_optimization_score: 72,
    interview_likelihood: "Medium"
  },
  jobDescription: "Software Engineer position requiring React, Node.js, and team leadership skills."
};

const sampleResumeHtml = `
<h1><PERSON></h1>
<p>Email: <EMAIL> | Phone: (*************</p>

<h2>Experience</h2>
<h3>Software Developer - Tech Company (2020-2023)</h3>
<ul>
  <li>Developed web applications using React and Node.js</li>
  <li>Worked on team projects</li>
  <li>Fixed bugs and implemented features</li>
</ul>

<h2>Skills</h2>
<ul>
  <li>JavaScript, React, Node.js</li>
  <li>HTML, CSS</li>
  <li>Git, GitHub</li>
</ul>

<h2>Education</h2>
<p>Bachelor of Science in Computer Science - University (2016-2020)</p>
`;

async function testChatAPI() {
  try {
    console.log('🚀 Testing Chat API...');
    
    // Connect to database
    await connectToDatabase();
    console.log('✅ Connected to database');

    // Create chat service
    const chatService = new ChatService();
    console.log('✅ Chat service created');

    // Test 1: Create a new chat session
    console.log('\n📝 Test 1: Creating chat session...');
    const sessionData = await chatService.createResumeFineTuneSession(
      '507f1f77bcf86cd799439011', // Sample user ID
      sampleResumeHtml,
      sampleMatchResult
    );
    
    console.log('✅ Session created:', {
      sessionId: sessionData.sessionId,
      firstQuestion: sessionData.firstQuestion.substring(0, 100) + '...',
      analysis: sessionData.analysis.substring(0, 100) + '...'
    });

    // Test 2: Send a user message
    console.log('\n💬 Test 2: Sending user message...');
    const userMessage = "I led a team of 5 developers and increased our deployment frequency by 50% through implementing CI/CD pipelines.";
    
    const response1 = await chatService.processMessage(sessionData.sessionId, userMessage);
    console.log('✅ Message processed:', {
      response: response1.response.substring(0, 100) + '...',
      isComplete: response1.isComplete
    });

    // Test 3: Send another message
    console.log('\n💬 Test 3: Sending another message...');
    const userMessage2 = "I also reduced bug reports by 30% by implementing automated testing and code review processes.";
    
    const response2 = await chatService.processMessage(sessionData.sessionId, userMessage2);
    console.log('✅ Second message processed:', {
      response: response2.response.substring(0, 100) + '...',
      isComplete: response2.isComplete
    });

    // Test 4: Continue until completion (AI-driven)
    console.log('\n🔄 Test 4: Continuing conversation until AI decides to complete...');
    let currentResponse = response2;
    let messageCount = 2;
    
    // More detailed responses to test AI decision making
    const detailedResponses = [
      "I managed a cross-functional team of 8 people including developers, designers, and QA engineers. We delivered 3 major product releases that increased user engagement by 40%.",
      "I have certifications in AWS Solutions Architect and Kubernetes. I also led the migration of our monolithic application to microservices, reducing deployment time from 2 hours to 15 minutes.",
      "I implemented automated testing that increased code coverage from 60% to 95% and reduced production bugs by 70%. I also mentored 3 junior developers who were promoted within 6 months."
    ];
    
    let responseIndex = 0;
    while (!currentResponse.isComplete && messageCount < 10) {
      messageCount++;
      const nextMessage = responseIndex < detailedResponses.length 
        ? detailedResponses[responseIndex] 
        : `Additional experience: I have ${messageCount} years of experience in software development with focus on scalable systems.`;
      
      responseIndex++;
      
      currentResponse = await chatService.processMessage(sessionData.sessionId, nextMessage);
      console.log(`✅ Message ${messageCount} processed:`, {
        response: currentResponse.response.substring(0, 100) + '...',
        isComplete: currentResponse.isComplete,
        aiDecision: currentResponse.isComplete ? 'COMPLETE' : 'CONTINUE'
      });
      
      if (currentResponse.isComplete) {
        console.log('🎯 AI decided to complete the conversation');
        break;
      }
    }

    // Test 5: Get chat history
    console.log('\n📚 Test 5: Getting chat history...');
    const chatHistory = await chatService.getChatHistory(sessionData.sessionId);
    console.log('✅ Chat history retrieved:', {
      sessionStatus: chatHistory.session.status,
      messageCount: chatHistory.messages.length,
      sessionType: chatHistory.session.sessionType
    });

    // Show final result if conversation is complete
    if (currentResponse.isComplete) {
      console.log('\n🎉 Conversation completed!');
      console.log('Final resume length:', currentResponse.finalResume?.length || 0);
      console.log('Number of changes:', currentResponse.changes?.length || 0);
      if (currentResponse.changes) {
        console.log('Sample changes:', currentResponse.changes.slice(0, 2));
      }
    }

    console.log('\n✅ All tests completed successfully!', currentResponse.finalResume);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testChatAPI().then(() => {
  console.log('🏁 Test script finished');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
