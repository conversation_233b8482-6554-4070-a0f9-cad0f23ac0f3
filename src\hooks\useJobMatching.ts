import { useState } from 'react';
import { authenticatedFetch } from '@/lib/auth/token-storage';
import { MatchResult } from '@/types/match';

export const useJobMatching = () => {
  const [jobDescription, setJobDescription] = useState('');
  const [jobUrl, setJobUrl] = useState('');
  const [inputType, setInputType] = useState<'text' | 'url'>('text');
  const [matchResult, setMatchResult] = useState<MatchResult | null>(null);
  const [matchLoading, setMatchLoading] = useState(false);

  // Handle job description input change
  const handleJobDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setJobDescription(e.target.value);
    // Clear previous match result when job description changes
    if (matchResult) {
      setMatchResult(null);
    }
  };

  // Handle job URL input change
  const handleJobUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setJobUrl(e.target.value);
    // Clear previous match result when job URL changes
    if (matchResult) {
      setMatchResult(null);
    }
  };

  // Handle input type toggle
  const handleInputTypeChange = (type: 'text' | 'url') => {
    setInputType(type);
    // Clear previous match result when input type changes
    if (matchResult) {
      setMatchResult(null);
    }
  };

  // Handle match button click
  const handleMatch = async (
    existingUser: string | null,
    setError: (error: string) => void
  ) => {
    if (inputType === 'text' && !jobDescription.trim()) {
      setError('Please enter a job description');
      return false;
    }

    if (inputType === 'url' && !jobUrl.trim()) {
      setError('Please enter a job posting URL');
      return false;
    }

    if (!existingUser) {
      setError('User authentication is required for matching');
      return false;
    }

    try {
      setMatchLoading(true);
      setError('');

      const response = await authenticatedFetch(`/api/users/${existingUser}/match`, {
        method: 'POST',
        body: JSON.stringify({
          jobDescription: inputType === 'text' ? jobDescription : '',
          jobUrl: inputType === 'url' ? jobUrl : '',
          inputType,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMatchResult(result.data);
        return true;
      } else {
        throw new Error(result.error?.message || 'Failed to match resume with job description');
      }
    } catch (err) {
      setError(`An error occurred: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error(err);
      return false;
    } finally {
      setMatchLoading(false);
    }
  };

  // Get job description for chat
  const getJobDescriptionForChat = () => {
    if (inputType === 'text') {
      return jobDescription;
    } else if (inputType === 'url' && matchResult?.jobDescription) {
      return matchResult.jobDescription;
    }
    return `Job posting from URL: ${jobUrl}`;
  };

  // Reset job matching state
  const resetJobMatching = () => {
    setJobDescription('');
    setJobUrl('');
    setInputType('text');
    setMatchResult(null);
  };

  return {
    jobDescription,
    jobUrl,
    inputType,
    matchResult,
    matchLoading,
    handleJobDescriptionChange,
    handleJobUrlChange,
    handleInputTypeChange,
    handleMatch,
    getJobDescriptionForChat,
    resetJobMatching,
  };
};
