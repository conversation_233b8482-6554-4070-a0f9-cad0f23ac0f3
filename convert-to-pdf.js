const fs = require('fs');
const path = require('path');
const PDFDocument = require('pdfkit');

// Create a document
const doc = new PDFDocument();

// Pipe its output to a file
doc.pipe(fs.createWriteStream('sample-resume.pdf'));

// Read the text file
const resumeText = fs.readFileSync('sample-resume.txt', 'utf8');

// Set some metadata
doc.info.Title = '<PERSON> Resume';
doc.info.Author = '<PERSON>';

// Add the text content
doc.fontSize(12);
doc.font('Helvetica');
doc.text(resumeText, {
  paragraphGap: 10,
  indent: 20,
  align: 'left',
  columns: 1
});

// Finalize the PDF and end the stream
doc.end();

console.log('PDF created successfully at sample-resume.pdf');
