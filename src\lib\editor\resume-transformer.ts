import { ParsedResume } from '../pdf/pdf-parser';

export interface ResumeData {
  skills: string[];
  education: string[];
  experience: string[];
  contact: {
    phone?: string;
    email?: string;
    address?: string;
  };
}

/**
 * Transform parsed resume data into a structured format
 * @param parsedResume The parsed resume data from PDF
 * @returns Structured resume data
 */
export async function transformResumeToEditorJS(parsedResume: ParsedResume): Promise<ResumeData> {
  try {
    // Return the parsed fields directly
    return {
      skills: parsedResume.skills || [],
      education: parsedResume.education || [],
      experience: parsedResume.experience || [],
      contact: parsedResume.contact || {},
    };
  } catch (error) {
    console.error('Error transforming resume:', error);
    // Return a fallback structure
    return {
      skills: [],
      education: [],
      experience: [],
      contact: {},
    };
  }
}

/**
 * Validate resume data structure
 * @param data The resume data to validate
 * @returns True if valid, false otherwise
 */
export function validateEditorJSData(data: unknown): data is ResumeData {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const obj = data as Record<string, unknown>;

  // Check if all required fields exist
  return (
    Array.isArray(obj.skills) &&
    Array.isArray(obj.education) &&
    Array.isArray(obj.experience) &&
    typeof obj.contact === 'object'
  );
}
