'use client';

import React from 'react';

export interface ProfileUrl {
  type: string;
  url: string;
}

export interface PersonalDetails {
  name: string;
  phone_numbers: string[];
  email_addresses: string[];
  profile_urls: ProfileUrl[];
  location: string;
}

export interface SummaryObjective {
  summary_text: string;
}

export interface SkillCategory {
  category: string;
  items: string[];
}

export interface WorkExperience {
  company_name: string;
  job_title: string;
  location: string;
  start_date: string;
  end_date: string;
  duration: string;
  responsibilities_achievements: string[];
}

export interface Education {
  degree: string;
  major: string;
  institution_name: string;
  location: string;
  graduation_date: string;
  gpa_score: string;
}

export interface Certification {
  certification_name: string;
  issuing_body: string;
  issue_date: string;
  expiration_date: string;
}

export interface OtherSections {
  awards_honors: string[];
  projects: string[];
  publications: string[];
  volunteer_experience: string[];
  interests: string[];
  references_available: boolean;
}

export interface ParsedResumeResponse {
  personal_details: PersonalDetails;
  summary_objective: SummaryObjective;
  skills: SkillCategory[];
  work_experience: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  other_sections: OtherSections;
}

interface ParsedResumeDisplayProps {
  data: ParsedResumeResponse;
}

const ParsedResumeDisplay: React.FC<ParsedResumeDisplayProps> = ({ data }) => {
  const {
    personal_details,
    summary_objective,
    skills,
    work_experience,
    education,
    certifications,
    other_sections
  } = data;

  const SectionCard: React.FC<{ title: string; children: React.ReactNode; icon?: string }> = ({ 
    title, 
    children, 
    icon = "📋" 
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <span className="text-xl">{icon}</span>
        {title}
      </h3>
      {children}
    </div>
  );

  const formatDate = (dateStr: string) => {
    if (!dateStr || dateStr.toLowerCase() === 'present') return dateStr;
    try {
      return new Date(dateStr).toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      });
    } catch {
      return dateStr;
    }
  };

  return (
    <div className="space-y-6">
      {/* Personal Details */}
      <SectionCard title="Personal Information" icon="👤">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Contact Details</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <p className="font-medium text-lg text-gray-800">{personal_details.name}</p>
              {personal_details.location && (
                <p className="flex items-center gap-1">
                  <span>📍</span> {personal_details.location}
                </p>
              )}
              {personal_details.phone_numbers.map((phone, index) => (
                <p key={index} className="flex items-center gap-1">
                  <span>📞</span> {phone}
                </p>
              ))}
              {personal_details.email_addresses.map((email, index) => (
                <p key={index} className="flex items-center gap-1">
                  <span>✉️</span> {email}
                </p>
              ))}
            </div>
          </div>
          
          {personal_details.profile_urls && personal_details.profile_urls.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Profile Links</h4>
              <div className="space-y-1">
                {personal_details.profile_urls.map((profile, index) => (
                  <a
                    key={index}
                    href={profile.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                  >
                    <span>🔗</span>
                    <span className="capitalize">{profile.type}</span>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </SectionCard>

      {/* Summary/Objective */}
      {summary_objective?.summary_text && (
        <SectionCard title="Professional Summary" icon="📝">
          <p className="text-gray-700 leading-relaxed">
            {summary_objective.summary_text}
          </p>
        </SectionCard>
      )}

      {/* Skills */}
      {skills && skills.length > 0 && (
        <SectionCard title="Skills" icon="⚡">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {skills.map((skillCategory, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2 capitalize">
                  {skillCategory.category}
                </h4>
                <div className="flex flex-wrap gap-1">
                  {skillCategory.items.map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {/* Work Experience */}
      {work_experience && work_experience.length > 0 && (
        <SectionCard title="Work Experience" icon="💼">
          <div className="space-y-6">
            {work_experience.map((job, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-4 pb-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-800">{job.job_title}</h4>
                    <p className="text-blue-600 font-medium">{job.company_name}</p>
                  </div>
                  <div className="text-sm text-gray-500 mt-1 sm:mt-0 sm:text-right">
                    <p>{formatDate(job.start_date)} - {formatDate(job.end_date)}</p>
                    <p>{job.duration}</p>
                    {job.location && <p>📍 {job.location}</p>}
                  </div>
                </div>
                
                {job.responsibilities_achievements && job.responsibilities_achievements.length > 0 && (
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 ml-2">
                    {job.responsibilities_achievements.map((item, itemIndex) => (
                      <li key={itemIndex}>{item}</li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {/* Education */}
      {education && education.length > 0 && (
        <SectionCard title="Education" icon="🎓">
          <div className="space-y-4">
            {education.map((edu, index) => (
              <div key={index} className="border-l-4 border-green-500 pl-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                  <div>
                    <h4 className="font-semibold text-gray-800">{edu.degree}</h4>
                    {edu.major && <p className="text-gray-600">{edu.major}</p>}
                    <p className="text-green-600 font-medium">{edu.institution_name}</p>
                    {edu.location && <p className="text-sm text-gray-500">📍 {edu.location}</p>}
                  </div>
                  <div className="text-sm text-gray-500 mt-1 sm:mt-0 sm:text-right">
                    {edu.graduation_date && <p>{formatDate(edu.graduation_date)}</p>}
                    {edu.gpa_score && <p>GPA: {edu.gpa_score}</p>}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {/* Certifications */}
      {certifications && certifications.length > 0 && (
        <SectionCard title="Certifications" icon="🏆">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {certifications.map((cert, index) => (
              <div key={index} className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800">{cert.certification_name}</h4>
                <p className="text-yellow-700 font-medium">{cert.issuing_body}</p>
                <div className="text-sm text-gray-600 mt-2">
                  {cert.issue_date && <p>Issued: {formatDate(cert.issue_date)}</p>}
                  {cert.expiration_date && (
                    <p>Expires: {formatDate(cert.expiration_date)}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </SectionCard>
      )}

      {/* Other Sections */}
      {other_sections && (
        <SectionCard title="Additional Information" icon="📌">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {other_sections.awards_honors && other_sections.awards_honors.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>🏅</span> Awards & Honors
                </h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {other_sections.awards_honors.map((award, index) => (
                    <li key={index}>{award}</li>
                  ))}
                </ul>
              </div>
            )}

            {other_sections.projects && other_sections.projects.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>🚀</span> Projects
                </h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {other_sections.projects.map((project, index) => (
                    <li key={index}>{project}</li>
                  ))}
                </ul>
              </div>
            )}

            {other_sections.publications && other_sections.publications.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>📚</span> Publications
                </h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {other_sections.publications.map((publication, index) => (
                    <li key={index}>{publication}</li>
                  ))}
                </ul>
              </div>
            )}

            {other_sections.volunteer_experience && other_sections.volunteer_experience.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>🤝</span> Volunteer Experience
                </h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {other_sections.volunteer_experience.map((volunteer, index) => (
                    <li key={index}>{volunteer}</li>
                  ))}
                </ul>
              </div>
            )}

            {other_sections.interests && other_sections.interests.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>🎯</span> Interests
                </h4>
                <div className="flex flex-wrap gap-1">
                  {other_sections.interests.map((interest, index) => (
                    <span
                      key={index}
                      className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {other_sections.references_available && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <span>📞</span> References
                </h4>
                <p className="text-sm text-gray-600">References available upon request</p>
              </div>
            )}
          </div>
        </SectionCard>
      )}
    </div>
  );
};

export default ParsedResumeDisplay;
