import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { MongoClient } from 'mongodb';
import connectToDatabase from '@/lib/mongodb';
import User, { IUser } from '@/models/User';
import { generateToken } from '@/lib/auth/jwt';

// Safely get environment variables with fallbacks
const MONGODB_URI = process.env.MONGODB_URI || '';
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';

// Only create MongoClient if MONGODB_URI is available
const client = MONGODB_URI ? new MongoClient(MONGODB_URI) : null;

const handler = NextAuth({
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID,
      clientSecret: GOOGLE_CLIENT_SECRET,
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          // Skip database operations during build time
          if (!MONGODB_URI) {
            console.warn('MongoDB URI not available, skipping database operations');
            return true;
          }

          await connectToDatabase();
          
          // Check if user already exists in our User collection
          let existingUser = await User.findOne({ email: user.email });
          
          if (!existingUser) {
            // Create new user in our User collection
            existingUser = new User({
              name: user.name,
              email: user.email,
              googleId: user.id,
              avatar: user.image || undefined,
              provider: 'google',
              roles: ['user'],
              fineTuneCredits: {
                remainingCredits: 1,
                totalUsed: 0,
                lastPurchasedCredits: 0,
              },
            });
            await existingUser.save();
          } else if (!existingUser.googleId) {
            // Link Google account to existing email user
            existingUser.googleId = user.id;
            existingUser.avatar = user.image || undefined;
            if (existingUser.provider === 'email') {
              existingUser.provider = 'google';
            }
            await existingUser.save();
          }
          
          return true;
        } catch (error) {
          console.error('Error during Google sign in:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      if (account?.provider === 'google' && user) {
        try {
          // Skip database operations during build time
          if (!MONGODB_URI) {
            console.warn('MongoDB URI not available, skipping JWT database operations');
            return token;
          }

          await connectToDatabase();
          const dbUser = await User.findOne({ email: user.email }) as IUser;
          
          if (dbUser) {
            // Generate our custom JWT token with serializable data
            const customToken = await generateToken({
              userId: (dbUser._id as any).toString(),
              email: dbUser.email,
              roles: [...dbUser.roles], // Spread array to ensure it's serializable
            });
            
            token.customToken = customToken;
            token.userId = (dbUser._id as any).toString();
            token.roles = [...dbUser.roles]; // Spread array to ensure it's serializable
            token.provider = 'google';
          }
        } catch (error) {
          console.error('Error in JWT callback:', error);
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token.customToken) {
        session.customToken = token.customToken as string;
        session.userId = token.userId as string;
        session.roles = token.roles as string[];
        session.provider = token.provider as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
  },
  debug: true,
  events: {
    async signIn({ user, account, profile }) {
      console.log('NextAuth signIn event:', { user: user.email, provider: account?.provider });
    },
    async createUser({ user }) {
      console.log('NextAuth createUser event:', user.email);
    },
  },
});

export { handler as GET, handler as POST };
