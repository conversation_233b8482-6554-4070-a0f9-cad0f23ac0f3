# Resume Parsing with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> Prompts

This directory contains the resume parsing functionality that uses <PERSON><PERSON><PERSON><PERSON> with markdown-based prompts to extract structured data from resume text.

## Overview

The resume parsing system consists of three main components:

1. **Markdown Prompts** (`src/prompts/parse-resume.md`) - The detailed parsing instructions in markdown format
2. **Prompt Reader** (`prompt-reader.ts`) - Utility to read markdown prompts and convert them to LangChain templates
3. **Resume Parser Service** (`resume-parser-service.ts`) - The main service that handles AI-powered resume parsing

## Features

- **Markdown-based prompts**: Easy to edit and maintain prompts in markdown format
- **LangChain integration**: Uses LangChain for AI model interaction
- **Multiple AI providers**: Supports both OpenAI and DeepSeek models
- **Structured output**: Returns well-defined TypeScript interfaces
- **Fallback handling**: Provides basic parsing when AI fails
- **Comprehensive parsing**: Extracts personal details, skills, experience, education, certifications, and more

## Quick Start

### Basic Usage

```typescript
import { parseResumeText } from './resume-parser-service';

// Parse resume text (typically extracted from PDF)
const resumeText = "<PERSON>\nSoftware Engineer\<EMAIL>...";
const parsedResume = await parseResumeText(resumeText);

console.log(parsedResume.personal_details.name); // "John Doe"
console.log(parsedResume.skills); // Array of skill categories
console.log(parsedResume.work_experience); // Array of work experiences
```

### Using the Service Class

```typescript
import { ResumeParserService } from './resume-parser-service';

const parser = new ResumeParserService();

try {
  const result = await parser.parseResume(resumeText);
  // Handle successful parsing
} catch (error) {
  // Use fallback parsing
  const fallback = parser.generateFallbackResponse(resumeText);
}
```

## Configuration

Set the following environment variables:

```bash
# AI Provider (openai or deepseek)
AI_PROVIDER=openai

# API Key for the chosen provider
AI_API_KEY=your_api_key_here

# Model name (optional, defaults to gpt-3.5-turbo for OpenAI)
LLM_MODEL_NAME=gpt-4
```

## Data Structure

The parser returns a `ParsedResumeResponse` object with the following structure:

```typescript
interface ParsedResumeResponse {
  personal_details: {
    name: string;
    phone_numbers: string[];
    email_addresses: string[];
    profile_urls: ProfileUrl[];
    location: string;
  };
  summary_objective: {
    summary_text: string;
  };
  skills: SkillCategory[];
  work_experience: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  other_sections: {
    awards_honors: string[];
    projects: string[];
    publications: string[];
    volunteer_experience: string[];
    interests: string[];
    references_available: boolean;
  };
}
```

## Integration with PDF Parsing

```typescript
import { parseResumeText } from './resume-parser-service';
import { extractTextFromPdf } from '../pdf/pdf-parser';

// Extract text from PDF first
const pdfText = await extractTextFromPdf(pdfBuffer);

// Then parse the extracted text
const structuredData = await parseResumeText(pdfText);
```

## API Route Integration

```typescript
// In your API route (e.g., /api/resume/parse)
import { parseResumeText } from '@/lib/ai/resume-parser-service';

export async function POST(request: Request) {
  try {
    const { resumeText } = await request.json();
    
    const parsedResume = await parseResumeText(resumeText);
    
    return Response.json({
      success: true,
      data: parsedResume
    });
  } catch (error) {
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
```

## Customizing Prompts

To modify the parsing behavior:

1. Edit `src/prompts/parse-resume.md` to change the parsing instructions
2. The prompt reader will automatically load the updated markdown content
3. No code changes needed - the system reads the markdown file at runtime

## Error Handling

The system includes multiple layers of error handling:

1. **AI Parsing Errors**: Falls back to basic text extraction
2. **JSON Parsing Errors**: Attempts to extract JSON from markdown code blocks
3. **Validation Errors**: Ensures required fields are present
4. **Network Errors**: Provides meaningful error messages

## Testing

See `resume-parser-example.ts` for comprehensive usage examples and test cases.

## File Structure

```
src/lib/ai/
├── README.md                    # This documentation
├── prompt-reader.ts             # Markdown prompt reader utility
├── resume-parser-service.ts     # Main parsing service
├── resume-parser-example.ts     # Usage examples and tests
└── chat-service.ts             # Existing chat service
```

## Dependencies

- `@langchain/core` - Core LangChain functionality
- `@langchain/openai` - OpenAI integration
- `@langchain/deepseek` - DeepSeek integration
- `fs` and `path` - File system operations for reading markdown prompts

## Best Practices

1. **Clean PDF Text**: Ensure PDF text extraction removes formatting artifacts
2. **Validate Results**: Always validate parsed results before using them
3. **Handle Failures**: Implement fallback strategies for AI parsing failures
4. **Monitor Usage**: Track AI API usage and costs
5. **Update Prompts**: Regularly review and improve parsing prompts based on results

## Troubleshooting

### Common Issues

1. **"Failed to read prompt from markdown"**: Check file path and permissions
2. **"AI response missing fields"**: Verify AI model is responding with valid JSON
3. **"Invalid response: not an object"**: Check AI API key and model availability
4. **TypeScript errors**: Ensure all interfaces are properly imported

### Debug Mode

Enable detailed logging by setting:

```bash
NODE_ENV=development
```

This will log the full AI responses and parsing steps for debugging.
