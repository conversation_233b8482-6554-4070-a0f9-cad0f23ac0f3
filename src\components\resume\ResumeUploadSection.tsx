'use client';

import React, { RefObject } from 'react';
import LoadingSpinner from '@/components/LoadingSpinner';

interface ResumeUploadSectionProps {
  resumeFile: File | null;
  uploading: boolean;
  error: string | null;
  fileInputRef: RefObject<HTMLInputElement | null>;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export default function ResumeUploadSection({
  resumeFile,
  uploading,
  error,
  fileInputRef,
  onFileChange,
  onSubmit,
}: ResumeUploadSectionProps) {
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-4">Upload Your Resume</h2>
      <p className="mb-6 text-gray-600">
        Upload your resume in PDF format to extract key information such as skills,
        education, work experience, and contact details.
      </p>

      {error && (
        <div className="mb-6 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={onSubmit}>
        <div className="mb-6">
          <label htmlFor="resume" className="block text-gray-700 mb-2 font-medium">
            Resume (PDF only)
          </label>
          <input
            type="file"
            id="resume"
            name="resume"
            accept="application/pdf"
            onChange={onFileChange}
            ref={fileInputRef}
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <button
          type="submit"
          disabled={uploading || !resumeFile}
          className={`w-full py-3 px-4 rounded text-white font-medium ${
            uploading || !resumeFile
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 transition-colors'
          }`}
        >
          {uploading ? (
            <div className="flex items-center justify-center">
              <LoadingSpinner size="small" />
              <span className="ml-2">Processing Resume...</span>
            </div>
          ) : (
            'Upload & Parse Resume'
          )}
        </button>
      </form>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-medium mb-3">How it works</h3>
        <ol className="list-decimal pl-5 space-y-2 text-gray-700">
          <li>Upload your resume in PDF format</li>
          <li>Our system will automatically extract key information</li>
          <li>Review the parsed data to ensure accuracy</li>
          <li>Use the extracted information for your applications</li>
        </ol>
      </div>
    </div>
  );
}
