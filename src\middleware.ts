import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { validateTokenFromRequest, createUnauthorizedResponse } from '@/lib/auth/jwt';

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Only log API requests
  if (pathname.startsWith('/api/')) {
    console.log(`[${new Date().toISOString()}] ${request.method} ${pathname}`);
  }
  
  // Protect the /app route with NextAuth session
  if (pathname.startsWith('/app')) {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
    
    if (!token) {
      // Redirect to login page if not authenticated
      const loginUrl = new URL('/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  // Check if this is an API route that needs authentication
  if (pathname.startsWith('/api/')) {
    // Allow user creation (POST to /api/users) and login
    if (pathname === '/api/users' && request.method === 'POST') {
      return NextResponse.next();
    }
    
    if (pathname === '/api/auth/login') {
      return NextResponse.next();
    }
    
    // Allow all NextAuth routes (OAuth, session management, etc.)
    if (pathname.startsWith('/api/auth/')) {
      return NextResponse.next();
    }
    
    // All other API routes require NextAuth authentication
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
    if (!token) {
      return createUnauthorizedResponse('Authentication required. Please log in.');
    }
    
    // Add user info to headers for API routes to use
    const response = NextResponse.next();
    response.headers.set('x-user-id', token.userId as string);
    response.headers.set('x-user-email', token.email as string);
    return response;
  }
  
  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: ['/api/:path*', '/app/:path*'],
};
