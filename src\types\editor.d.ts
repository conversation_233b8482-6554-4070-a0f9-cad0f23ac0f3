declare module '@tiptap/react' {
  import { Editor } from '@tiptap/core';
  
  export interface EditorProps {
    extensions?: unknown[];
    content?: string;
    editable?: boolean;
    autofocus?: boolean | 'start' | 'end' | number;
    editorProps?: Record<string, unknown>;
    parseOptions?: Record<string, unknown>;
    onBeforeCreate?: (props: { editor: Editor }) => void;
    onCreate?: (props: { editor: Editor }) => void;
    onUpdate?: (props: { editor: Editor }) => void;
    onSelectionUpdate?: (props: { editor: Editor }) => void;
    onTransaction?: (props: { editor: Editor; transaction: unknown }) => void;
    onFocus?: (props: { editor: Editor; event: FocusEvent }) => void;
    onBlur?: (props: { editor: Editor; event: FocusEvent }) => void;
    onDestroy?: (props: { editor: Editor }) => void;
  }
  
  export interface BubbleMenuProps {
    editor: Editor;
    tippyOptions?: Record<string, unknown>;
    shouldShow?: (props: { editor: Editor; view: unknown; state: unknown; oldState?: unknown; from: number; to: number }) => boolean;
    updateDelay?: number;
    children: React.ReactNode;
  }
  
  export interface FloatingMenuProps {
    editor: Editor;
    tippyOptions?: Record<string, unknown>;
    shouldShow?: (props: { editor: Editor; view: unknown; state: unknown; oldState?: unknown; from: number; to: number }) => boolean;
    updateDelay?: number;
    children: React.ReactNode;
  }
  
  export function useEditor(options: EditorProps): Editor | null;
  export function EditorContent(props: { editor: Editor | null; [key: string]: unknown }): JSX.Element;
  export function BubbleMenu(props: BubbleMenuProps): JSX.Element;
  export function FloatingMenu(props: FloatingMenuProps): JSX.Element;
}

declare module '@tiptap/extension-image' {
  import { Node } from '@tiptap/core';
  const Image: Node;
  export default Image;
}

declare module '@tiptap/extension-link' {
  import { Mark } from '@tiptap/core';
  const Link: Mark;
  export default Link;
}

declare module '@tiptap/extension-underline' {
  import { Mark } from '@tiptap/core';
  const Underline: Mark;
  export default Underline;
}

declare module '@tiptap/extension-strike' {
  import { Mark } from '@tiptap/core';
  const Strike: Mark;
  export default Strike;
}

declare module '@tiptap/extension-highlight' {
  import { Mark } from '@tiptap/core';
  const Highlight: Mark;
  export default Highlight;
}

declare module '@tiptap/extension-table' {
  import { Node } from '@tiptap/core';
  const Table: Node;
  export default Table;
}

declare module '@tiptap/extension-table-row' {
  import { Node } from '@tiptap/core';
  const TableRow: Node;
  export default TableRow;
}

declare module '@tiptap/extension-table-cell' {
  import { Node } from '@tiptap/core';
  const TableCell: Node;
  export default TableCell;
}

declare module '@tiptap/extension-table-header' {
  import { Node } from '@tiptap/core';
  const TableHeader: Node;
  export default TableHeader;
}

declare module '@tiptap/starter-kit' {
  import { Extension } from '@tiptap/core';
  const StarterKit: Extension;
  export default StarterKit;
}

declare module '@tiptap/extension-heading' {
  import { Node } from '@tiptap/core';
  const Heading: Node;
  export default Heading;
}

declare module '@tiptap/extension-document' {
  import { Node } from '@tiptap/core';
  const Document: Node;
  export default Document;
}

declare module '@tiptap/extension-paragraph' {
  import { Node } from '@tiptap/core';
  const Paragraph: Node;
  export default Paragraph;
}

declare module '@tiptap/extension-text' {
  import { Node } from '@tiptap/core';
  const Text: Node;
  export default Text;
}
