import { useState, useEffect, useCallback } from 'react';

interface FineTuneCreditsData {
  remainingCredits: number;
  totalUsed: number;
  lastPurchasedCredits: number;
  firstUsedAt?: string;
  lastUsedAt?: string;
  canFineTune: boolean;
  isFirstTime: boolean;
}

interface UseFineTuneCreditsReturn {
  creditsData: FineTuneCreditsData | null;
  loading: boolean;
  error: string | null;
  refetchCredits: () => Promise<void>;
}

export function useFineTuneCredits(userId: string | null): UseFineTuneCreditsReturn {
  const [creditsData, setCreditsData] = useState<FineTuneCreditsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCredits = useCallback(async () => {
    if (!userId) {
      setCreditsData(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${userId}/finetune/usage`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || 'Failed to fetch credits');
      }

      if (result.success) {
        setCreditsData(result.data);
      } else {
        throw new Error(result.error?.message || 'Failed to fetch credits');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching fine-tune credits:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const refetchCredits = useCallback(async () => {
    await fetchCredits();
  }, [fetchCredits]);

  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  return {
    creditsData,
    loading,
    error,
    refetchCredits,
  };
}
